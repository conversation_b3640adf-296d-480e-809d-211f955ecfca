/**
 * @file    kernel/memory/mm.c
 * <AUTHOR>
 * @brief
 * @version 3.0.0
 * @date    2024-07-30
 *
 * ac006b61 2024-07-02 移除一级ttos目录
 * b041d869 2024-05-15 格式化代码并处理一些头文件依赖问题
 * 4fbd5c22 2024-04-22 添加elf加载
 * 3f6e557e 2024-03-30 移除一些打印信息
 * 3d961570 2024-03-30 修复 浮点初始化 增加MMU
 *
 * 科东(广州)软件科技有限公司 版权所有
 * @copyright Copyright (C) 2023 Intewell Inc. All Rights Reserved.
 */

#include "page.h"
#include <assert.h>
#include <commonTypes.h>
#include <ttosMM.h>
#include <memblock.h>
#include <system/kconfig.h>

#define KLOG_TAG "MM"
#include <klog.h>

extern int    __end__;
extern int    __executable_start;
extern size_t kernel_mmu_get_page_size (void);
extern int    kernel_mmu_init (void);
static bool g_page_allocer_inited = false;

void multiboot_init (void);
void multiboot_mem_scan (void (*mem_add_func)(phys_addr_t addr, phys_addr_t size, void *ctx), 
                        void (*mem_reserve_func)(phys_addr_t addr, phys_addr_t size, void *ctx));

int fdt_foreach_memory(void (*func)(phys_addr_t addr, phys_addr_t size, void *ctx), void *ctx);
int fdt_foreach_memory_reserved(void (*func)(phys_addr_t addr, phys_addr_t size, void *ctx), void *ctx);

static void mem_enum (phys_addr_t addr, phys_addr_t size, void *ctx)
{
    ttos_memblk_add(addr, size);
    page_mem_regions_set(addr, size);
}

static void mem_enum_reserved (phys_addr_t addr, phys_addr_t size, void *ctx)
{
    ttos_memblk_reserve(addr, size);
}

bool page_allocer_inited(void)
{
    return g_page_allocer_inited;
}
/**
 * @brief 内存管理器初始化
 *
 * @return INT32_T 0: 初始化完成 其他: 错误
 */
int32_t ttosMemoryManagerInit (void)
{
    phys_addr_t start, size;
        /* arm架构下earlycon初始化通过设备树解析得到基地址，x86跳过这一步 */
    if(!IS_ENABLED(__x86_64__))
    {
        fdt_foreach_memory(mem_enum, NULL);
        fdt_foreach_memory_reserved(mem_enum_reserved, NULL);
    }
    else
    {
        multiboot_init ();
        multiboot_mem_scan (mem_enum, mem_enum_reserved);
    }

    start = (phys_addr_t)(virt_addr_t)&__executable_start + get_kernel_mm()->pv_offset;
    size = (virt_addr_t)&__end__ - (virt_addr_t)&__executable_start;
    size = ALIGN_UP(size, PAGE_SIZE);
    ttos_memblk_reserve (start, size);

    start = (phys_addr_t)(virt_addr_t)KERNEL_SPACE_START + get_kernel_mm()->pv_offset;
    ttos_memblk_reserve (start, 0x80000);

#ifdef CONFIG_MODULES
    start = ALIGN_UP((phys_addr_t)(virt_addr_t)&__end__ + get_kernel_mm()->pv_offset, PAGE_SIZE);
    size = 0x4000000;
    ttos_memblk_reserve (start, size);
#endif

    if(IS_ENABLED(CONFIG_X86_64))
    {
        void memblk_dump_all (void);
        memblk_dump_all ();
    }
    kernel_mmu_init ();
    ttos_memblk_release_mem_to_pagealloc();
    g_page_allocer_inited = true;
    return 0;
}

T_ULONG ttosGetPageSize (T_VOID)
{
    return kernel_mmu_get_page_size ();
}

struct page_obj *ttosPageAllocObj (T_UWORD count)
{
    struct page_obj *obj = malloc (sizeof (struct page_obj));
    if (obj == NULL)
    {
        return NULL;
    }
    obj->page_count = count;
    obj->page_start = page_address(pages_alloc(page_bits(count << PAGE_SIZE_SHIFT), ZONE_NORMAL));
    if (obj->page_start == 0)
    {
        free (obj);
        return NULL;
    }
    return obj;
}
int32_t ttosPageGetInfo (uintptr_t *total_nr, uintptr_t *free_nr)
{
    page_get_info (total_nr, free_nr);
    return 0;
}
