/**
 * @file    drivers/dm/zero.c
 * <AUTHOR>
 * @brief
 * @version 3.0.0
 * @date    2024-07-30
 *
 * 0c106b3f 2024-07-24 修改规范INIT API
 * ac006b61 2024-07-02 移除一级ttos目录
 * 008dc447 2024-06-03 添加ascii zero null设备
 *
 * 科东(广州)软件科技有限公司 版权所有
 * @copyright Copyright (C) 2023 Intewell Inc. All Rights Reserved.
 */

#include <errno.h>
#include <fcntl.h>
#include <fs/fs.h>
#include <fs/kpoll.h>
#include <klog.h>
#include <stdbool.h>
#include <string.h>
#include <ttos_init.h>

/****************************************************************************
 * Private Function Prototypes
 ****************************************************************************/

static ssize_t devkmsg_read(struct file *filep, char *buffer, size_t buflen);
static ssize_t devkmsg_write(struct file *filep, const char *buffer, size_t buflen);
static int devkmsg_poll(struct file *filep, struct kpollfd *fds, bool setup);

/****************************************************************************
 * Private Data
 ****************************************************************************/

static const struct file_operations g_devkmsg_fops = {
    .read = devkmsg_read,   /* read */
    .write = devkmsg_write, /* write */
    .poll = devkmsg_poll    /* poll */
};

/****************************************************************************
 * Private Functions
 ****************************************************************************/

/****************************************************************************
 * Name: devkmsg_read
 ****************************************************************************/

static ssize_t devkmsg_read(struct file *filep, char *buffer, size_t len)
{
    unsigned int pos = filep->f_pos;
    int size = klog_read_msg(buffer, &pos, len, !!(filep->f_oflags & O_NONBLOCK));
    if (size == 0 && !(filep->f_oflags & O_NONBLOCK))
    {
        // return -EAGAIN;
        return 0;
    }
    filep->f_pos = pos;
    return size;
}

/****************************************************************************
 * Name: devkmsg_write
 ****************************************************************************/

static ssize_t devkmsg_write(struct file *filep, const char *buffer, size_t len)
{
    return len;
}

/****************************************************************************
 * Name: devkmsg_poll
 ****************************************************************************/

static int devkmsg_poll(struct file *filep, struct kpollfd *fds, bool setup)
{
    if (setup)
    {
        kpoll_notify(&fds, 1, POLLIN | POLLOUT, NULL, NULL);
    }

    return 0;
}

/****************************************************************************
 * Public Functions
 ****************************************************************************/

/****************************************************************************
 * Name: devkmsg_register
 *
 * Description:
 *   Register /dev/zero
 *
 ****************************************************************************/

static int devkmsg_register(void)
{
    return register_driver("/dev/kmsg", &g_devkmsg_fops, 0666, NULL);
}
INIT_EXPORT_DRIVER(devkmsg_register, "devkmsg_register");
