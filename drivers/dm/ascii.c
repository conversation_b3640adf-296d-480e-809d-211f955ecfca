/**
 * @file    drivers/dm/ascii.c
 * <AUTHOR>
 * @brief
 * @version 3.0.0
 * @date    2024-07-30
 * 
 * 0c106b3f 2024-07-24 修改规范INIT API
 * d9aca9a7 2024-07-02 lwip 库化
 * ac006b61 2024-07-02 移除一级ttos目录
 * 008dc447 2024-06-03 添加ascii zero null设备
 * 
 * 科东(广州)软件科技有限公司 版权所有
 * @copyright Copyright (C) 2023 Intewell Inc. All Rights Reserved.
*/

#include <fs/fs.h>
#include <fs/kpoll.h>
#include <ttos_init.h>

#define PRINTABLE_FIRST 0x20
#define PRINTABLE_COUNT (0x7f - 0x20)

/****************************************************************************
 * Private Function Prototypes
 ****************************************************************************/

static ssize_t devascii_read (struct file *filep, char *buffer, size_t buflen);
static ssize_t devascii_write (struct file *filep, const char *buffer,
                               size_t buflen);
static int devascii_poll (struct file *filep, struct kpollfd *fds, bool setup);

/****************************************************************************
 * Private Data
 ****************************************************************************/

static const struct file_operations g_devascii_fops = {
    .read  = devascii_read,  /* read */
    .write = devascii_write, /* write */
    .poll  = devascii_poll   /* poll */
};

/****************************************************************************
 * Private Functions
 ****************************************************************************/

/****************************************************************************
 * Name: devascii_read
 ****************************************************************************/

static ssize_t devascii_read (struct file *filep, char *buffer, size_t len)
{
    size_t i;
    for (i = 0; i < len; i++)
    {
        buffer[i] = PRINTABLE_FIRST + (filep->f_pos + i) % PRINTABLE_COUNT;

        /* Replace the space character with a newline */

        if (buffer[i] == PRINTABLE_FIRST)
        {
            buffer[i] = '\n';
        }
    }

    filep->f_pos += len;
    return len;
}

/****************************************************************************
 * Name: devascii_write
 ****************************************************************************/

static ssize_t devascii_write (struct file *filep, const char *buffer,
                               size_t len)
{
    return len;
}

/****************************************************************************
 * Name: devascii_poll
 ****************************************************************************/

static int devascii_poll (struct file *filep, struct kpollfd *fds, bool setup)
{
    if (setup)
    {
        kpoll_notify (&fds, 1, POLLIN | POLLOUT, NULL, NULL);
    }

    return 0;
}

/****************************************************************************
 * Public Functions
 ****************************************************************************/

/****************************************************************************
 * Name: devascii_register
 *
 * Description:
 *   Register /dev/ascii
 *
 ****************************************************************************/

static int devascii_register (void)
{
    return register_driver ("/dev/ascii", &g_devascii_fops, 0666, NULL);
}
INIT_EXPORT_DRIVER (devascii_register, "devascii_register");