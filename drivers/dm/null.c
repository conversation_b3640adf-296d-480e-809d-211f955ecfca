/**
 * @file    drivers/dm/null.c
 * <AUTHOR>
 * @brief
 * @version 3.0.0
 * @date    2024-07-30
 * 
 * 0c106b3f 2024-07-24 修改规范INIT API
 * d9aca9a7 2024-07-02 lwip 库化
 * ac006b61 2024-07-02 移除一级ttos目录
 * 008dc447 2024-06-03 添加ascii zero null设备
 * 
 * 科东(广州)软件科技有限公司 版权所有
 * @copyright Copyright (C) 2023 Intewell Inc. All Rights Reserved.
*/

#include <fs/fs.h>
#include <fs/kpoll.h>
#include <ttos_init.h>

/****************************************************************************
 * Private Function Prototypes
 ****************************************************************************/

static ssize_t devnull_read (struct file *filep, char *buffer, size_t buflen);
static ssize_t devnull_write (struct file *filep, const char *buffer,
                              size_t buflen);
static int devnull_poll (struct file *filep, struct kpollfd *fds, bool setup);

/****************************************************************************
 * Private Data
 ****************************************************************************/

static const struct file_operations g_devnull_fops = {
    .read  = devnull_read,  /* read */
    .write = devnull_write, /* write */
    .poll  = devnull_poll   /* poll */
};

/****************************************************************************
 * Private Functions
 ****************************************************************************/

/****************************************************************************
 * Name: devnull_read
 ****************************************************************************/

static ssize_t devnull_read (struct file *filep, char *buffer, size_t len)
{
    return 0; /* Return EOF */
}

/****************************************************************************
 * Name: devnull_write
 ****************************************************************************/

static ssize_t devnull_write (struct file *filep, const char *buffer,
                              size_t len)
{
    return len; /* Say that everything was written */
}

/****************************************************************************
 * Name: devnull_poll
 ****************************************************************************/

static int devnull_poll (struct file *filep, struct kpollfd *fds, bool setup)
{
    if (setup)
    {
        kpoll_notify (&fds, 1, POLLIN | POLLOUT, NULL, NULL);
    }

    return 0;
}

/****************************************************************************
 * Public Functions
 ****************************************************************************/

/****************************************************************************
 * Name: devnull_register
 *
 * Description:
 *   Register /dev/null
 *
 ****************************************************************************/

static int devnull_register (void)
{
    return register_driver ("/dev/null", &g_devnull_fops, 0666, NULL);
}
INIT_EXPORT_DRIVER (devnull_register, "devnull_register");
