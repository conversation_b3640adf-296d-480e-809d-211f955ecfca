/**
 * @file    drivers/dm/zero.c
 * <AUTHOR>
 * @brief
 * @version 3.0.0
 * @date    2024-07-30
 * 
 * 0c106b3f 2024-07-24 修改规范INIT API
 * ac006b61 2024-07-02 移除一级ttos目录
 * 008dc447 2024-06-03 添加ascii zero null设备
 * 
 * 科东(广州)软件科技有限公司 版权所有
 * @copyright Copyright (C) 2023 Intewell Inc. All Rights Reserved.
*/

#include <fs/fs.h>
#include <fs/kpoll.h>
#include <stdbool.h>
#include <string.h>
#include <ttos_init.h>

/****************************************************************************
 * Private Function Prototypes
 ****************************************************************************/

static ssize_t devzero_read (struct file *filep, char *buffer, size_t buflen);
static ssize_t devzero_write (struct file *filep, const char *buffer,
                              size_t buflen);
static int devzero_poll (struct file *filep, struct kpollfd *fds, bool setup);

/****************************************************************************
 * Private Data
 ****************************************************************************/

static const struct file_operations g_devzero_fops = {
    .read  = devzero_read,  /* read */
    .write = devzero_write, /* write */
    .poll  = devzero_poll   /* poll */
};

/****************************************************************************
 * Private Functions
 ****************************************************************************/

/****************************************************************************
 * Name: devzero_read
 ****************************************************************************/

static ssize_t devzero_read (struct file *filep, char *buffer, size_t len)
{
    memset (buffer, 0, len);
    return len;
}

/****************************************************************************
 * Name: devzero_write
 ****************************************************************************/

static ssize_t devzero_write (struct file *filep, const char *buffer,
                              size_t len)
{
    return len;
}

/****************************************************************************
 * Name: devzero_poll
 ****************************************************************************/

static int devzero_poll (struct file *filep, struct kpollfd *fds, bool setup)
{
    if (setup)
    {
        kpoll_notify (&fds, 1, POLLIN | POLLOUT, NULL, NULL);
    }

    return 0;
}

/****************************************************************************
 * Public Functions
 ****************************************************************************/

/****************************************************************************
 * Name: devzero_register
 *
 * Description:
 *   Register /dev/zero
 *
 ****************************************************************************/

static int devzero_register (void)
{
    return register_driver ("/dev/zero", &g_devzero_fops, 0666, NULL);
}
INIT_EXPORT_DRIVER (devzero_register, "devzero_register");
