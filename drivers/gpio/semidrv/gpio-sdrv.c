/***************************************************************************
 * Included Files
 ***************************************************************************/
#include "gpio-sdrv.h"
#include "page.h"
#include <assert.h>
#include <errno.h>
#include <stdbool.h>
#include <stdint.h>
#include <string.h>
#include <sys/types.h>
#include <system/bitops.h>
#include <unistd.h>
#include <fs/fs.h>
#include <fs/ioctl.h>
#include <stdio.h>
#include <driver/devbus.h>
#include <driver/device.h>
#include <driver/driver.h>
#include <driver/of.h>
#include <ttosProcess.h>
#include <ttosMM.h>
#include <ttos_init.h>
#include <ttos_pic.h>
#include <io.h>
//#include <fio_mux.h>
#undef KLOG_TAG
#define KLOG_TAG "GPIO"
#include <klog.h>

struct gpio_priv_s {
    struct gpio_obj gpio;
    u32 instance_id;
    phys_addr_t reg_addr;
    uintptr_t base_addr;
    size_t reg_size;
    u32 irq[SDRV_GPIO_NPORTS];
    u32 irq_priority;
    u32 gpio_count;             // 管理的 GPIO 数量
    void * udata;          // 用户数据
    u8 name[GPIO_NAME_MAX];

    /* The following is a list if poll structures of threads waiting for
     * driver events. The 'struct pollfd' reference for each open is also
     * retained in the f_priv field of the 'struct file'.
     */
    ttos_spinlock_t polllock;
    struct kpollfd *d_fds;
    struct aio_info aio;
};

static int fgpio_open(struct file *filep);
static int fgpio_close(struct file *filep);
static ssize_t fgpio_read(struct file *filep, char *buffer, size_t buflen);
static ssize_t fgpio_write(struct file *filep, const char *buffer, size_t buflen);
static int fgpio_ioctl(struct file *filep,unsigned int cmd, unsigned long arg);
static int fgpio_poll (struct file *filep, struct kpollfd *fds, bool setup);
static int fgpio_mmap (struct file *filep, struct mm_region *map);

static int kgpio_int_set(struct gpio_desc *, uint32_t state);
static int kgpio_mode(struct gpio_desc *, uint32_t mode);
static int kgpio_read(struct gpio_desc *);
static int kgpio_write(struct gpio_desc *, uint32_t state);

static const struct file_operations g_gpio_fops = {
    .open = fgpio_open,
    .close = fgpio_close,
    .read = fgpio_read,
    .write = fgpio_write,
    .ioctl = fgpio_ioctl,
    .poll  = fgpio_poll,
    .mmap = fgpio_mmap,
};

struct gpio_obj_ops g_gpio_obj_ops = 
{
    .int_set = kgpio_int_set,
    .mode = kgpio_mode,
    .read = kgpio_read,
    .write = kgpio_write,
};

static struct gpio_priv_s *g_gpio_privs[MAX_SDRV_GPIO_CON] = {NULL};

/**
 * 设置GPIO引脚的方向
 * @param base_addr GPIO控制器的基地址
 * @param pin 引脚编号
 * @param direction 输入或输出方向（GPIO_DIRECTION_INPUT 或 GPIO_DIRECTION_OUTPUT）
 * @return 成功返回0，失败返回负数错误码
 */
int sdrv_gpio_set_direction(uintptr_t base_addr, uint32_t pin, uint32_t direction)
{
    uint32_t reg_val;
    static int32_t ret_val;
    uint32_t pin_index;
    uint32_t  gpio_idx;

    gpio_idx = pin / SDRV_NPORT_GPIO_PIN;
    pin_index = pin % SDRV_NPORT_GPIO_PIN;

    if (direction != GPIO_DIRECTION_INPUT && direction != GPIO_DIRECTION_OUTPUT)
    {
        return -EINVAL; // 无效参数
    }
    reg_val = readl(base_addr + GPIO_DIR_PORT_X(gpio_idx));

    KLOG_I("LDbg{%s}[%d]. pin:%xH, pin_idx:%u, baseaddr:%llxH, offset:%xH, reg_val:%xH", \
        __func__, __LINE__, pin, pin_index, base_addr, GPIO_DIR_PORT_X(gpio_idx), reg_val);

    ret_val = 0;

    if(direction == GPIO_DIRECTION_INPUT)
    {
        reg_val &= ~(BIT(pin_index));
    }
    else if(direction == GPIO_DIRECTION_OUTPUT)
    {
        reg_val |= (BIT(pin_index));
    }
    else
    {
        KLOG_W("ERROR, Invalid IO flag");
        ret_val = -EINVAL;
    }

    if(ret_val == 0){
        writel(reg_val, base_addr + GPIO_DIR_PORT_X(gpio_idx));
    }

    KLOG_I("Setting GPIO pin %u direction to %s", pin, direction == GPIO_DIRECTION_INPUT ? "INPUT" : "OUTPUT");
    return ret_val;
}

/**
 * 获取GPIO引脚的方向
 * @param base_addr GPIO控制器的基地址
 * @param pin 引脚编号
 * @return 引脚方向（GPIO_DIRECTION_INPUT 或 GPIO_DIRECTION_OUTPUT），失败返回负数错误码
 */
int sdrv_gpio_get_direction(uintptr_t base_addr, uint32_t pin)
{
    uint32_t reg_val;
    uint32_t pin_index;
    uint32_t direction;
    uint32_t  gpio_idx;

    gpio_idx = pin / SDRV_NPORT_GPIO_PIN;
    pin_index = pin % SDRV_NPORT_GPIO_PIN;

    reg_val = readl(base_addr + GPIO_DIR_PORT_X(gpio_idx));
    KLOG_I("LDbg{%s}[%d]. pin:%xH, pin_idx:%u, baseaddr:%llxH, offset:%xH, reg_val 0x%x", __func__, __LINE__, pin, pin_index, base_addr, GPIO_DIR_PORT_X(gpio_idx), reg_val);

    if ((reg_val & (BIT(pin_index))) == 0)
    {
        direction = GPIO_DIRECTION_INPUT;
    }
    else if ((reg_val & (BIT(pin_index))) == BIT(pin_index))
    {
        direction = GPIO_DIRECTION_OUTPUT;
    }
    else
    {
        return -1;
    }

    KLOG_I("Getting GPIO pin %u direction with %s", pin, direction == GPIO_DIRECTION_INPUT ? "INPUT" : "OUTPUT");
    return direction;
}

/**
 * 设置GPIO引脚的值
 * @param base_addr GPIO控制器的基地址
 * @param pin 引脚编号
 * @param value 引脚值（0或1）
 * @return 成功返回0，失败返回负数错误码
 */
int sdrv_gpio_set_value(uintptr_t base_addr, uint32_t pin, uint32_t value)
{
    uint32_t reg_val;
    unsigned int pin_index;
    uint32_t  gpio_idx;

    if (value != 0 && value != 1)
    {
        return -EINVAL; // 无效参数
    }

    gpio_idx = pin / SDRV_NPORT_GPIO_PIN;
    pin_index = pin % SDRV_NPORT_GPIO_PIN;
    
    reg_val = readl(base_addr + GPIO_DIR_PORT_X(gpio_idx));
    KLOG_I("LDbg{%s}[%d]. pin_idx:%u, baseaddr:%llxH, offset:%xH, val:%x, reg_val:%x", __func__, __LINE__, pin_index, base_addr, GPIO_DIR_PORT_X(gpio_idx), value, reg_val);
    if ((reg_val & (BIT(pin_index))) == 0)
    {
        KLOG_E("pin %d is input, can't set value", pin);
        return -1;
    }

    reg_val = readl(base_addr + GPIO_DATA_OUT_PORT_X(gpio_idx));//读出当前的IO值
    if(value == 0)
    {
        reg_val &= ~(BIT(pin_index));
        writel(reg_val, base_addr + GPIO_DATA_OUT_PORT_X(gpio_idx));
    }
    else if(value == 1)
    {
        reg_val |= (BIT(pin_index));
        writel(reg_val, base_addr + GPIO_DATA_OUT_PORT_X(gpio_idx));
    }
    else
    {
        KLOG_W("ERROR, Invalid value");
    }

    KLOG_I("Setting GPIO pin %u value to %u", pin, value);
    return (value < 2) ? TRUE : -1;
}

/**
 * 获取GPIO引脚的值
 * @param base_addr GPIO控制器的基地址
 * @param pin 引脚编号
 * @return 引脚值（0或1），失败返回负数错误码
 */
int sdrv_gpio_get_value(uintptr_t base_addr, uint32_t pin)
{
    uint32_t reg_val;
    uint32_t value;
    unsigned int pin_index;
    uint32_t  gpio_idx;

    gpio_idx = pin / SDRV_NPORT_GPIO_PIN;
    pin_index = pin % SDRV_NPORT_GPIO_PIN;

    reg_val = readl(base_addr + GPIO_DATA_IN_PORT_X(gpio_idx));//读出当前的IO值
    KLOG_I("LDbg{%s}[%d]. pin_idx:%u, baseaddr:%llxH, offset:%xH, reg_val:%x", __func__, __LINE__, pin_index, base_addr, GPIO_DATA_IN_PORT_X(gpio_idx), reg_val);

    if ((reg_val & (BIT(pin_index))) == 0)
    {
        value = 0;
    }
    else if ((reg_val & (BIT(pin_index))) == BIT(pin_index))
    {
        value = 1;
    }
    else
    {
        return -1;
    }

    KLOG_I("Getting GPIO pin %u value %d", pin, value);
    return value; // 示例返回值
}

void handle_gpio_int (uint32_t irq, void *param)
{
    uint32_t bit = 0;
    uint32_t  gpio_idx;
    unsigned long clear_int;

    struct gpio_desc *desc = (struct gpio_desc *)param;
    struct gpio_priv_s *priv = (struct gpio_priv_s *)desc->parent->priv;
    gpio_idx = desc->gpio_id / SDRV_NPORT_GPIO_PIN; 

    uint32_t irq_status = readl(priv->base_addr + GPIO_INT_STATUS_PORT_X(gpio_idx));
    uint32_t irq_mask = readl(priv->base_addr + GPIO_INT_MASK_PORT_X(gpio_idx));
 
    for_each_set_bit(bit, (unsigned long *)&irq_status, 32){
        clear_int = readl(priv->base_addr + GPIO_INT_EDGE_CLR_PORT_X(gpio_idx));
		/* clear INT */
        writel( clear_int | BIT(bit), priv->base_addr + GPIO_INT_EDGE_CLR_PORT_X(gpio_idx));
		/* Invoke interrupt handler */
		if (desc->int_handle)
        {
            desc->int_handle(desc);
        }
	}

}

int sdrv_gpio_set_int(uintptr_t base_addr, uint32_t pin, uint32_t value)
{
    uint32_t reg_val;
    unsigned int pin_index;
    uint32_t  gpio_idx;

    if (value != 0 && value != 1)
    {
        return -EINVAL; // 无效参数
    }

    gpio_idx = pin / SDRV_NPORT_GPIO_PIN;
    pin_index = pin % SDRV_NPORT_GPIO_PIN;

    reg_val = readl(base_addr + GPIO_INT_EN_PORT_X(gpio_idx));
    KLOG_I("LDbg{%s}[%d]. pin_idx:%u, baseaddr:%llxH, offset:%xH, value:%x, reg_val=%x", __func__, __LINE__, pin_index, base_addr, GPIO_INT_EN_PORT_X(gpio_idx), value, reg_val);
    if (value == 0)
    {
        reg_val &= ~(BIT(pin_index));
    }
    else if (value == 1)
    {
        reg_val |= (BIT(pin_index));
    }
    else
    {
        KLOG_W("ERROR, Invalid value");
        return -EINVAL;
    }

    writel(reg_val, base_addr + GPIO_INT_EN_PORT_X(gpio_idx));

    KLOG_I("Setting GPIO pin %u interrupt to %u", pin, value);
    return 0;
}

int sdrv_gpio_set_int_mode(uintptr_t base_addr, uint32_t pin, enum gpio_int_mode state)
{
    uint32_t reg_int_en, reg_int_level, reg_int_mask, reg_int_polarity;
    unsigned int pin_index;
    uint32_t  gpio_idx;

    gpio_idx = pin / SDRV_NPORT_GPIO_PIN;
    pin_index = pin % SDRV_NPORT_GPIO_PIN;

    reg_int_en = readl(base_addr + GPIO_INT_EN_PORT_X(gpio_idx));
    reg_int_level = readl(base_addr + GPIO_INT_TYPE_PORT_X(gpio_idx));
    reg_int_mask = readl(base_addr + GPIO_INT_MASK_PORT_X(gpio_idx));
    reg_int_polarity = readl(base_addr + GPIO_INT_POL_PORT_X(gpio_idx));
    switch (state)
    {
        case GPIO_INT_MODE_DISABLE:
            reg_int_en &= ~(BIT(pin_index));
            break;
        case GPIO_INT_MODE_RISING:
            reg_int_level |= (BIT(pin_index));
            reg_int_polarity |= (BIT(pin_index));
            break;
        case GPIO_INT_MODE_FALLING:
            reg_int_level |= (BIT(pin_index));
            reg_int_polarity &= ~(BIT(pin_index));
            break;
        case GPIO_INT_MODE_LOWLEVEL:
            reg_int_level &= ~(BIT(pin_index));
            reg_int_polarity &= ~(BIT(pin_index));
            break;
        case GPIO_INT_MODE_HIGHLEVEL:
            reg_int_level &= ~(BIT(pin_index));
            reg_int_polarity |= (BIT(pin_index));
            break;
        default:
            KLOG_W("ERROR, Invalid value");
            return -EINVAL;
    }

    writel(reg_int_en, base_addr + GPIO_INT_EN_PORT_X(gpio_idx));
    writel(reg_int_level, base_addr + GPIO_INT_TYPE_PORT_X(gpio_idx));
    writel(reg_int_mask, base_addr + GPIO_INT_MASK_PORT_X(gpio_idx));
    writel(reg_int_polarity, base_addr + GPIO_INT_POL_PORT_X(gpio_idx));

    KLOG_I("Setting GPIO pin %u interrupt mode to %d", pin, state);
    return 0;
}

static int kgpio_int_set(struct gpio_desc *desc, uint32_t state)
{
    struct gpio_priv_s *priv;
    int start_no = (desc->parent)->start_no;
    uint32_t  gpio_idx;

    if (desc == NULL)
    {
        KLOG_E("%s %s", __func__, "ERROR, Invalid parameter");
        return -1;
    }

    priv = (struct gpio_priv_s *)desc->parent->priv;
    gpio_idx = desc->gpio_id / SDRV_NPORT_GPIO_PIN;

    KLOG_I("LDbg{%s}[%d]. pin_idx:%u, baseaddr:%llxH, idx:%x", __func__, __LINE__, desc->gpio_id, priv->base_addr, gpio_idx);
    if (state == GPIO_INT_ENABLE)
    {
        sdrv_gpio_set_int(priv->base_addr,  desc->gpio_id, 1);
        sdrv_gpio_set_int_mode(priv->base_addr, desc->gpio_id, GPIO_INT_MODE_RISING);
        ttos_pic_irq_install (priv->irq[gpio_idx], handle_gpio_int, desc, 0, priv->name);
        ttos_pic_irq_unmask (priv->irq[gpio_idx]);
    }
    else
    {
        sdrv_gpio_set_int(priv->base_addr, desc->gpio_id, 0);
        ttos_pic_irq_mask (priv->irq[gpio_idx]);
    }

    return 0;
}
static int kgpio_mode(struct gpio_desc *desc, uint32_t mode)
{
    struct gpio_priv_s *priv;

    if (desc == NULL)
    {
        KLOG_E("%s %s", __func__, "ERROR, Invalid parameter");
        return -1;
    }
    int start_no = (desc->parent)->start_no;

    priv = (struct gpio_priv_s *)desc->parent->priv;

    if (mode == GPIO_MODE_INPUT)
    {
        sdrv_gpio_set_direction(priv->base_addr, desc->gpio_id, GPIO_DIRECTION_INPUT);
    }
    else if (mode == GPIO_MODE_OUTPUT)
    {
        sdrv_gpio_set_direction(priv->base_addr, desc->gpio_id, GPIO_DIRECTION_OUTPUT);
    }
    else if (mode == GPIO_MODE_INT_EDGE)
    {
        sdrv_gpio_set_int_mode(priv->base_addr, desc->gpio_id, GPIO_INT_MODE_RISING);
    }
    else if (mode == GPIO_MODE_INT_LEVEL)
    {
        sdrv_gpio_set_int_mode(priv->base_addr, desc->gpio_id, GPIO_INT_MODE_HIGHLEVEL);
    }
    else
    {
        KLOG_W("ERROR, Invalid value");
        return -EINVAL;
    }

    return 0;
}

static int kgpio_read(struct gpio_desc *desc)
{
    struct gpio_priv_s *priv;
    int start_no = (desc->parent)->start_no;

    priv = (struct gpio_priv_s *)desc->parent->priv;

    return sdrv_gpio_get_value(priv->base_addr, desc->gpio_id);
}

static int kgpio_write(struct gpio_desc *desc, uint32_t state)
{
    struct gpio_priv_s *priv;
    int start_no = (desc->parent)->start_no;

    priv = (struct gpio_priv_s *)desc->parent->priv;

    return sdrv_gpio_set_value(priv->base_addr, desc->gpio_id, state);
}

static int fgpio_open(struct file *filep)
{
    struct gpio_priv_s *priv;
    priv = filep->f_inode->i_private;

    // Initialize GPIO hardware
    // e.g., sdrv_gpio_init(priv->base_addr);

    KLOG_I("%s opened", priv->name);

    return 0;
}

static int fgpio_close(struct file *filep) 
{
    struct gpio_priv_s *priv;
    priv = filep->f_inode->i_private;

    // Deinitialize GPIO hardware if necessary
    // e.g., sdrv_gpio_deinit(priv->base_addr);

    KLOG_I("%s closed", priv->name);

    return 0;
}

static ssize_t fgpio_read(struct file *filep, char *buffer, size_t buflen) {
    struct gpio_priv_s *priv;
    priv = filep->f_inode->i_private;

    // Implement GPIO read functionality
    // e.g., read from hardware registers

    KLOG_I("%s read", priv->name);

    return 0;
}

static ssize_t fgpio_write(struct file *filep, const char *buffer, size_t buflen)
{
    struct gpio_priv_s *priv;
    priv = filep->f_inode->i_private;

    // Implement GPIO write functionality
    // e.g., write to hardware registers

    KLOG_I("%s write", priv->name);

    return 0;
}

void poll_gpio_handle(struct gpio_desc *desc)
{
    struct gpio_priv_s *priv = NULL;
    uint32_t *irq_status = NULL;
    uint32_t *irq_data = NULL;

    if (desc)
    {
        priv = (struct gpio_priv_s *)(desc->parent->priv);
        if (priv)
        {
            if (priv->udata)
            {
                irq_data = (uint32_t *)priv->udata;
                /* 用户态从数组0中读取信息 */
                irq_data[0] = desc->gpio_id; //填写管脚号供用户态程序读取
            }

            if (priv->aio.aio_enable)
            {
                kpoll_notify (&priv->d_fds, 1, POLLIN, &priv->aio);
            }
        }
    }
}

static int fgpio_ioctl(struct file *filep,unsigned int cmd, unsigned long arg)
{
    struct gpio_priv_s *priv;
    struct gpio_config_s *config = (struct gpio_config_s *)arg;
    struct gpio_desc *desc = NULL;
    int ret = 0;

    priv = filep->f_inode->i_private;

    switch (cmd) {
        case FIOASYNC:
            {
                priv->aio.aio_enable = !!arg;
            }
            break;
        case FIOGETOWN:
            {
                if (arg)
                {
                    *(int *)arg = priv->aio.pid;
                }
            }
            break;
        case FIOSETOWN:
            {
                if (arg)
                {
                    priv->aio.pid = (int)arg;
                }
                else
                {
                    ret = -EINVAL;
                }
            }
            break;
        case GPIO_IOC_SET_DIRECTION:
            ret = sdrv_gpio_set_direction(priv->base_addr, config->pin, config->direction);
            break;
        case GPIO_IOC_GET_DIRECTION:
            config->direction = sdrv_gpio_get_direction(priv->base_addr, config->pin);
            if ((config->direction != GPIO_DIRECTION_INPUT) && (config->direction != GPIO_DIRECTION_OUTPUT))
            {
                ret = -1;
            }
            break;
        case GPIO_IOC_SET_VALUE:
            ret = sdrv_gpio_set_value(priv->base_addr, config->pin, config->value);
            break;
        case GPIO_IOC_GET_VALUE:
            config->value = sdrv_gpio_get_value(priv->base_addr, config->pin);
            if (config->value != 0 && config->value != 1)
            {
                ret = -1;
            }
            break;
        case GPIO_IOC_SET_CONFIG:
           // FIOPadSetGpioMux(priv->instance_id, config->pin);
            sdrv_gpio_set_direction(priv->base_addr, config->pin, config->direction);
            sdrv_gpio_set_value(priv->base_addr, config->pin, config->value);
            break;
        case GPIO_IOC_GET_CONFIG:
            config->direction = sdrv_gpio_get_direction(priv->base_addr, config->pin);
            if (config->direction < 0)
            {
                ret = -1;
            }

            config->value = sdrv_gpio_get_value(priv->base_addr, config->pin);
            if (config->value < 0)
            {
                ret = -1;
            }
            break;
        case GPIO_IOC_SET_MUX:
            /* 设置GPIO复用功能 */
           // FIOPadSetGpioMux(priv->instance_id, config->pin);
            break;
            
        case GPIO_IOC_SET_INT_MODE:
            ret = sdrv_gpio_set_int_mode(priv->base_addr, config->pin, config->irqmode);
            break;

        case GPIO_IOC_EN_INT:
            if (config->pin >= SDRV_MAX_GPIO_PIN)
            {
                return -EINVAL;
            }
            desc = priv->gpio.desc + config->pin;
            gpio_int_handle_set(desc, poll_gpio_handle);
            ret = kgpio_int_set(desc, GPIO_INT_ENABLE);
            break;

        case GPIO_IOC_LED_INIT:
            {
                struct gpioLedpincfg *config = (struct gpioLedpincfg *)arg;
                gpioLedpincfg_t.mSTPin = config->mSTPin;
                gpioLedpincfg_t.mDataPin = config->mDataPin;
                gpioLedpincfg_t.mSHPin = config->mSHPin;
                gpioLedpincfg_t.mMRPin = config->mMRPin;
                gpioLedpincfg_t.mOEPin = config->mOEPin;
                gpioLedpincfg_t.mdlyCnt = config->mdlyCnt;
                gpioLedpincfg_t.mShiftNum = config->mShiftNum;
            
                sdrv_gpio_set_direction(priv->base_addr, gpioLedpincfg_t.mSTPin, GPIO_DIRECTION_OUT);
                sdrv_gpio_set_direction(priv->base_addr, gpioLedpincfg_t.mDataPin, GPIO_DIRECTION_OUT);
                sdrv_gpio_set_direction(priv->base_addr, gpioLedpincfg_t.mSHPin, GPIO_DIRECTION_OUT);
                sdrv_gpio_set_direction(priv->base_addr, gpioLedpincfg_t.mMRPin, GPIO_DIRECTION_OUT);
                sdrv_gpio_set_direction(priv->base_addr, gpioLedpincfg_t.mOEPin, GPIO_DIRECTION_OUT);
            }
            break;
       
       case GPIO_IOC_HID_INIT:
            {
                struct gpioHidpincfg *config = (struct gpioHidpincfg *)arg;
                gpioHidpincfg_t.mGpioClk = config->mGpioClk;
                gpioHidpincfg_t.mGpioPl = config->mGpioPl;
                gpioHidpincfg_t.mGpioCe = config->mGpioCe;
                gpioHidpincfg_t.mGpioData = config->mGpioData;
                gpioHidpincfg_t.mShiftNum = config->mShiftNum;
                gpioHidpincfg_t.mdlyCnt = config->mdlyCnt;
 
                sdrv_gpio_set_direction(priv->base_addr, gpioHidpincfg_t.mGpioClk, GPIO_DIRECTION_OUT);
                sdrv_gpio_set_direction(priv->base_addr, gpioHidpincfg_t.mGpioPl, GPIO_DIRECTION_OUT);
                sdrv_gpio_set_direction(priv->base_addr, gpioHidpincfg_t.mGpioCe, GPIO_DIRECTION_OUT);
                sdrv_gpio_set_direction(priv->base_addr, gpioHidpincfg_t.mGpioData, GPIO_DIRECTION_IN);
            }
            break;

        case GPIO_IOC_HID_INPUT:
            {
                uint32_t* data= (uint32_t*)arg;
 
                int i = 0;
                uint32_t idata = 0UL, datareg = 0UL;
                sdrv_gpio_set_value(priv->base_addr, gpioHidpincfg_t.mGpioClk, GPIO_OUT);
                sdrv_gpio_set_value(priv->base_addr, gpioHidpincfg_t.mGpioPl, GPIO_IN);
                usleep(gpioHidpincfg_t.mdlyCnt);
                sdrv_gpio_set_value(priv->base_addr, gpioHidpincfg_t.mGpioPl, GPIO_OUT);
                usleep(gpioHidpincfg_t.mdlyCnt);
                sdrv_gpio_set_value(priv->base_addr, gpioHidpincfg_t.mGpioCe, GPIO_IN);
                sdrv_gpio_set_value(priv->base_addr, gpioHidpincfg_t.mGpioClk, GPIO_IN);

                for (i = 0; i < gpioHidpincfg_t.mShiftNum; i++)
                {
                    idata = (idata << 1UL);
                    datareg = (uint32_t)sdrv_gpio_get_value(priv->base_addr,gpioHidpincfg_t.mGpioData);
                    if (datareg != 0UL) /*从高位16到低位0进行串行写入*/
                    {
                        idata |= 0x01UL;
                    }
                    sdrv_gpio_set_value(priv->base_addr, gpioHidpincfg_t.mGpioClk, GPIO_IN);
                    usleep(gpioHidpincfg_t.mdlyCnt);
                    sdrv_gpio_set_value(priv->base_addr, gpioHidpincfg_t.mGpioClk, GPIO_OUT);
                    usleep(gpioHidpincfg_t.mdlyCnt);
                }

                data[0] = idata;

            }
            break;
        case GPIO_IOC_LED_OUTPUT:
            {
                uint32_t* data= (uint32_t*)arg;

                int i = 0;
                uint32_t TmpVal = data[0];
                sdrv_gpio_set_value(priv->base_addr, gpioLedpincfg_t.mOEPin, GPIO_IN); /*使能*/
                sdrv_gpio_set_value(priv->base_addr, gpioLedpincfg_t.mMRPin, GPIO_OUT); /*禁止清零*/
                sdrv_gpio_set_value(priv->base_addr, gpioLedpincfg_t.mSTPin, GPIO_IN); /*开始移位*/
                for (i = 0; i < gpioLedpincfg_t.mShiftNum; i++)
                {
                    sdrv_gpio_set_value(priv->base_addr, gpioLedpincfg_t.mSHPin, GPIO_IN);
                    if ((TmpVal & 0x01UL) == 0x01UL)
                    {
                        sdrv_gpio_set_value(priv->base_addr, gpioLedpincfg_t.mDataPin, GPIO_OUT);
                    }
                    else
                    {
                        sdrv_gpio_set_value(priv->base_addr, gpioLedpincfg_t.mDataPin, GPIO_IN);
                    }

                    TmpVal = (TmpVal >> 1);
                    usleep(gpioLedpincfg_t.mdlyCnt);
                    sdrv_gpio_set_value(priv->base_addr, gpioLedpincfg_t.mSHPin, GPIO_OUT);
                    usleep(gpioLedpincfg_t.mdlyCnt);
                }
                sdrv_gpio_set_value(priv->base_addr, gpioLedpincfg_t.mSHPin, GPIO_IN);
                sdrv_gpio_set_value(priv->base_addr, gpioLedpincfg_t.mSTPin, GPIO_OUT);
            }
            break;
        default:
            return -EINVAL;
    }

    KLOG_I("%s ioctl cmd: %x, ret =%d", priv->name, cmd, ret);

    return ret;
}

static int fgpio_poll (struct file *filep, struct kpollfd *fds, bool setup)
{
    struct gpio_priv_s *priv = NULL;
    irq_flags_t flags;

    if (NULL == filep)
    {
        return -EINVAL;
    }

    priv = filep->f_inode->i_private;

    if (NULL == priv)
    {
        return -EINVAL;
    }
    spin_lock_irqsave(&priv->polllock, flags);
    if(setup)
        priv->d_fds = fds;
    else
        priv->d_fds = NULL;
    spin_unlock_irqrestore(&priv->polllock, flags);

    return 0;
}

static int fgpio_unmap (void *task_group, struct mm_region *entry,
                         void *start, size_t length)
{
    return 0;
}

int fgpio_mmap (struct file *filep, struct mm_region *map)
{
    int len = 0x1000;
    struct mm *mm = NULL;
    struct mm_region *uregion = NULL;
    uintptr_t attr = 0;
    pcb_t pcb = NULL;
    struct gpio_priv_s *priv = NULL;

    if (NULL == filep)
    {
        return -EINVAL;
    }

    if (NULL == map)
    {
        return -EINVAL;
    }

    priv = filep->f_inode->i_private;

    if (NULL == priv->udata)
    {
        priv->udata = (void *)page_address(pages_alloc(page_bits(len), ZONE_NORMAL));
        if (NULL == priv->udata)
        {
            return -ENOMEM;
        }
        memset ((void *)priv->udata, 0, len);
    }

    map->priv.p = NULL;
    map->munmap = NULL;
    map->physical_address = mm_kernel_v2p((virt_addr_t)priv->udata);
    map->is_page_free = false;
    map->region_page_count = len >> PAGE_SIZE_SHIFT;

    return 0;
}

static int get_gpio_group_index(phys_addr_t reg_addr)
{
    int i = 0;
    int ret = -1;
    phys_addr_t addr = 0;

    if (reg_addr == FGPIO1_BASE_ADDR) ret = 1;
    else if (reg_addr == FGPIO2_BASE_ADDR) ret = 2;
    else if (reg_addr == FGPIO3_BASE_ADDR) ret = 3;
    else if (reg_addr == FGPIO4_BASE_ADDR) ret = 4;
    else if (reg_addr == FGPIO5_BASE_ADDR) ret = 5;

    return ret;
}

static int semidrive_gpio_probe(struct device *dev)
{
    int ret = 0;
    int irq_count = 0;
    struct gpio_priv_s *priv = NULL;
    struct gpio_obj *gpio = NULL;
    uint32_t value[4] = {0};
    uint32_t interrupts[3] = {0}, reg = 0, ngpios = 0;
    uint32_t idx = 0;

    if (NULL == dev || NULL == dev->of_node || NULL == dev->of_node->child)
    {
        return -EINVAL;
    }

    ret = of_property_read_u32_array (dev->of_node, "reg", &value[0], 4);
    if (ret)
    {
        ret = -EINVAL;
        goto errout;
    }

	if (dev->of_node && dev->of_node->child)
    {
        struct device_node *child = dev->of_node->child;
        priv = calloc(1, sizeof(struct gpio_priv_s));
        if (priv == NULL)
        {
            ret = -ENOMEM;
            KLOG_E("No enough memory");
            goto errout;
        }

        priv->reg_addr = ((phys_addr_t)value[0] << 32) | value[1];
        priv->reg_size = value[3];
        priv->instance_id = get_gpio_group_index(priv->reg_addr);

        if (priv->instance_id >= MAX_SDRV_GPIO_CON)
        {
            ret = -ENXIO;
            goto errout;
        }
        of_property_read_u32_array(child, "nr-gpios", &ngpios, 1);
        for (; child; child = child->sibling)
        {   
            priv->irq[idx] = ttos_pic_irq_alloc(child->device, 0);
            ttos_pic_irq_mask(priv->irq[idx]);
            idx++;
        }              
        priv->gpio_count = ngpios * idx;

        snprintf(priv->name, sizeof(dev->name), "/dev/gpio%d", priv->instance_id);

        ret = register_driver(priv->name, &g_gpio_fops, 0666, priv);
        if (ret)
        {
            KLOG_E("register driver %s failed", priv->name);
            goto errout;
        }

        spin_lock_init(&priv->polllock);

        gpio = calloc(1, sizeof(struct gpio_obj));
        gpio = &priv->gpio;
        gpio->priv = (void *)priv;
        gpio->mmio.paddr = priv->reg_addr;
        gpio->mmio.size = priv->reg_size;

        gpio->parent   = dev;
        gpio->ops      = &g_gpio_obj_ops;
        gpio->ngpio    = priv->gpio_count;
        gpio->start_no = 0;

        ret = gpio_controller_register(gpio);
        if (ret != 0)
        {
            goto errout;
        }

        priv->base_addr = gpio->mmio.vaddr;
        if ((void *)priv->base_addr == NULL)
        {
            KLOG_E("gpio_probe: reg_addr: 0x%x, base_addr: 0x%x", priv->reg_addr, priv->base_addr);
            goto errout;
        }
	}
    return 0;
errout:
    if (priv)
    {
        free(priv);
    }
    return ret;
}

static struct of_device_id gpio_table[] = {
    { .compatible = "semidrive,sdrv-gpio" },
    { /* end of list */ },
};

static struct driver gpio_init = {
    .name = "gpio",
    .probe = semidrive_gpio_probe,
    .match_table = &gpio_table[0],
};

int semidrive_gpio_init(void) {
    platform_add_driver(&gpio_init);
}
INIT_EXPORT_DRIVER(semidrive_gpio_init, "semidrive gpio init");
