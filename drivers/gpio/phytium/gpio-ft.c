/***************************************************************************
 * Included Files
 ***************************************************************************/
#include "gpio-ft.h"
#include "page.h"
#include <assert.h>
#include <errno.h>
#include <stdbool.h>
#include <stdint.h>
#include <string.h>
#include <sys/types.h>
#include <unistd.h>
#include <fs/fs.h>
#include <fs/ioctl.h>
#include <stdio.h>
#include <driver/devbus.h>
#include <driver/device.h>
#include <driver/driver.h>
#include <driver/of.h>
#include <ttosProcess.h>
#include <ttosMM.h>
#include <ttos_init.h>
#include <ttos_pic.h>
#include <io.h>
#include <fio_mux.h>
#undef KLOG_TAG
#define KLOG_TAG "GPIO"
#include <klog.h>
#define GPIO_NAME_MAX 256

#define GPIO_DIRECTION_INPUT  0
#define GPIO_DIRECTION_OUTPUT 1

#define GPIO_SWPORTA_DR_OFFSET 0x00U    /* WR Port A Output Data Register */
#define GPIO_SWPORTA_DDR_OFFSET 0x04U   /* WR Port A Data Direction Register */
#define GPIO_EXT_PORTA_OFFSET 0x08U     /* RO Port A Input Data Register */
#define GPIO_SWPORTB_DR_OFFSET 0x0cU    /* WR Port B Output Data Register */
#define GPIO_SWPORTB_DDR_OFFSET 0x10U   /* WR Port B Data Direction Register */
#define GPIO_EXT_PORTB_OFFSET 0x14U     /* RO Port B Input Data Register */

#define GPIO_INTEN_OFFSET 0x18U         /* WR Port A Interrput Enable Register */
#define GPIO_INTMASK_OFFSET 0x1cU       /* WR Port A Interrupt Mask Register */
#define GPIO_INTTYPE_LEVEL_OFFSET 0x20U /* WR Port A Interrupt Level Register */
#define GPIO_INT_POLARITY_OFFSET 0x24U  /* WR Port A Interrupt Polarity Register */
#define GPIO_INTSTATUS_OFFSET 0x28U     /* RO Port A Interrupt Status Register */
#define GPIO_RAW_INTSTATUS_OFFSET 0x2cU /* RO Port A Raw Interrupt Status Register */
#define GPIO_LS_SYNC_OFFSET 0x30U       /* WR Level-sensitive Synchronization Enable Register */
#define GPIO_DEBOUNCE_OFFSET 0x34U      /* WR Debounce Enable Register */
#define GPIO_PORTA_EOI_OFFSET 0x38U     /* WO Port A Clear Interrupt Register */
#define MAX_FT_GPIO_CON 6

#define FT_IRQ_GPIO_CELLS (3)
#define FT_MAX_GPIO_PIN (16)

/* GPIO */
#ifndef FGPIO0_BASE_ADDR
#define FGPIO0_BASE_ADDR (0x28034000U)
#define FGPIO1_BASE_ADDR (0x28035000U)
#define FGPIO2_BASE_ADDR (0x28036000U)
#define FGPIO3_BASE_ADDR (0x28037000U)
#define FGPIO4_BASE_ADDR (0x28038000U)
#define FGPIO5_BASE_ADDR (0x28039000U)
#endif

struct gpio_priv_s {
    struct gpio_obj gpio;
    u32 instance_id;
    phys_addr_t reg_addr;
    uintptr_t base_addr;
    size_t reg_size;
    u32 irq[FT_MAX_GPIO_PIN];
    u32 irq_priority;
    u32 gpio_count;             // 管理的 GPIO 数量
    void * udata;          // 用户数据
    u8 name[GPIO_NAME_MAX];

    /* The following is a list if poll structures of threads waiting for
     * driver events. The 'struct pollfd' reference for each open is also
     * retained in the f_priv field of the 'struct file'.
     */
    ttos_spinlock_t polllock;
    struct kpollfd *d_fds;
    struct aio_info aio;
};

static int fgpio_open(struct file *filep);
static int fgpio_close(struct file *filep);
static ssize_t fgpio_read(struct file *filep, char *buffer, size_t buflen);
static ssize_t fgpio_write(struct file *filep, const char *buffer, size_t buflen);
static int fgpio_ioctl(struct file *filep, unsigned int cmd, unsigned long arg);
static int fgpio_poll (struct file *filep, struct kpollfd *fds, bool setup);
static int fgpio_mmap (struct file *filep, struct mm_region *map);

static int kgpio_int_set(struct gpio_desc *, uint32_t state);
static int kgpio_mode(struct gpio_desc *, uint32_t mode);
static int kgpio_read(struct gpio_desc *);
static int kgpio_write(struct gpio_desc *, uint32_t state);

static const struct file_operations g_gpio_fops = {
    .open = fgpio_open,
    .close = fgpio_close,
    .read = fgpio_read,
    .write = fgpio_write,
    .ioctl = fgpio_ioctl,
    .poll  = fgpio_poll,
    .mmap = fgpio_mmap,
};

struct gpio_obj_ops g_gpio_obj_ops = 
{
    .int_set = kgpio_int_set,
    .mode = kgpio_mode,
    .read = kgpio_read,
    .write = kgpio_write,
};

static struct gpio_priv_s *g_gpio_privs[MAX_FT_GPIO_CON] = {NULL};

/**
 * 设置GPIO引脚的方向
 * @param base_addr GPIO控制器的基地址
 * @param pin 引脚编号
 * @param direction 输入或输出方向（GPIO_DIRECTION_INPUT 或 GPIO_DIRECTION_OUTPUT）
 * @return 成功返回0，失败返回负数错误码
 */
int e2000_gpio_set_direction(uintptr_t base_addr, uint32_t pin, uint32_t direction)
{
    uint32_t reg_val;
    static int32_t ret_val;
    uint32_t pin_index;

    pin_index = pin & 0x00000fffU;

    if (direction != GPIO_DIRECTION_INPUT && direction != GPIO_DIRECTION_OUTPUT)
    {
        return -EINVAL; // 无效参数
    }
    reg_val = readl(base_addr + GPIO_SWPORTA_DDR_OFFSET);
    KLOG_I("LDbg{%s}[%d]. pin:%xH, pin_idx:%u, baseaddr:%xH reg_val:%xH\n", \
        __func__, __LINE__, pin, pin_index, base_addr, reg_val);

    ret_val = 0;

    if(direction == GPIO_DIRECTION_INPUT)
    {
        reg_val &= ~(BIT(pin_index));
    }
    else if(direction == GPIO_DIRECTION_OUTPUT)
    {
        reg_val |= (BIT(pin_index));
    }
    else
    {
        KLOG_W("ERROR, Invalid IO flag\n");
        ret_val = -EINVAL;
    }

    if(ret_val == 0){
        writel(reg_val, base_addr + GPIO_SWPORTA_DDR_OFFSET);
    }

    KLOG_I("Setting GPIO pin %u direction to %s\n", pin, direction == GPIO_DIRECTION_INPUT ? "INPUT" : "OUTPUT");
    return ret_val;
}

/**
 * 获取GPIO引脚的方向
 * @param base_addr GPIO控制器的基地址
 * @param pin 引脚编号
 * @return 引脚方向（GPIO_DIRECTION_INPUT 或 GPIO_DIRECTION_OUTPUT），失败返回负数错误码
 */
int e2000_gpio_get_direction(uintptr_t base_addr, uint32_t pin)
{
    uint32_t reg_val;
    uint32_t pin_index;
    uint32_t direction;

    pin_index = pin & 0x00000fffU;

    reg_val = readl(base_addr + GPIO_SWPORTA_DDR_OFFSET);
    KLOG_I("LDbg{%s}[%d]. pin:%xH, pin_idx:%u, baseaddr:%xH, reg_val 0x%x\n", __func__, __LINE__, pin, pin_index, base_addr, reg_val);

    if ((reg_val & (BIT(pin_index))) == 0)
    {
        direction = GPIO_DIRECTION_INPUT;
    }
    else if ((reg_val & (BIT(pin_index))) == BIT(pin_index))
    {
        direction = GPIO_DIRECTION_OUTPUT;
    }
    else
    {
        return -1;
    }

    KLOG_I("Getting GPIO pin %u direction with %s\n", pin, direction == GPIO_DIRECTION_INPUT ? "INPUT" : "OUTPUT");
    return direction;
}

/**
 * 设置GPIO引脚的值
 * @param base_addr GPIO控制器的基地址
 * @param pin 引脚编号
 * @param value 引脚值（0或1）
 * @return 成功返回0，失败返回负数错误码
 */
int e2000_gpio_set_value(uintptr_t base_addr, uint32_t pin, uint32_t value)
{
    uint32_t reg_val;
    unsigned int pin_index;

    if (value != 0 && value != 1)
    {
        return -EINVAL; // 无效参数
    }

    pin_index = pin & 0x00000fffU;
    KLOG_I("LDbg{%s}[%d]. pin_idx:%u, baseaddr:%xH, val:%x\n", __func__, __LINE__, pin_index, base_addr, value);//add for test, add by lin
    reg_val = readl(base_addr + GPIO_SWPORTA_DDR_OFFSET);
    if ((reg_val & (BIT(pin_index))) == 0)
    {
        KLOG_E("pin %d is input, can't set value\n", pin);
        return -1;
    }

    reg_val = readl(base_addr + GPIO_SWPORTA_DR_OFFSET);//读出当前的IO值
    if(value == 0)
    {
        reg_val &= ~(BIT(pin_index));
        writel(reg_val, base_addr + GPIO_SWPORTA_DR_OFFSET);
    }
    else if(value == 1)
    {
        reg_val |= (BIT(pin_index));
        writel(reg_val, base_addr + GPIO_SWPORTA_DR_OFFSET);
    }
    else
    {
        KLOG_W("ERROR, Invalid value\n");
    }

    KLOG_I("Setting GPIO pin %u value to %u\n", pin, value);
    return (value < 2) ? TRUE : -1;
}

/**
 * 获取GPIO引脚的值
 * @param base_addr GPIO控制器的基地址
 * @param pin 引脚编号
 * @return 引脚值（0或1），失败返回负数错误码
 */
int e2000_gpio_get_value(uintptr_t base_addr, uint32_t pin)
{
    uint32_t reg_val;
    uint32_t value;
    unsigned int pin_index;

    pin_index = pin & 0x00000fffU;
    reg_val = readl(base_addr + GPIO_EXT_PORTA_OFFSET);//读出当前的IO值
    KLOG_I("LDbg{%s}[%d]. pin_idx:%u, baseaddr:%xH, reg_val:%x\n", __func__, __LINE__, pin_index, base_addr, reg_val);

    if ((reg_val & (BIT(pin_index))) == 0)
    {
        value = 0;
    }
    else if ((reg_val & (BIT(pin_index))) == BIT(pin_index))
    {
        value = 1;
    }
    else
    {
        return -1;
    }

    KLOG_I("Getting GPIO pin %u value %d\n", pin, value);
    return value; // 示例返回值
}

void handle_gpio_int (uint32_t irq, void *param)
{
    struct gpio_desc *desc = (struct gpio_desc *)param;
    struct gpio_priv_s *priv = (struct gpio_priv_s *)desc->parent->priv;
    uint32_t irq_status = readl(priv->base_addr + GPIO_INTSTATUS_OFFSET);
    uint32_t irq_raw_status = readl(priv->base_addr + GPIO_RAW_INTSTATUS_OFFSET);
    uint32_t irq_mask = readl(priv->base_addr + GPIO_INTMASK_OFFSET);
    uint32_t i;

    KLOG_I("GPIO %d interrupt status: 0x%x, raw status: 0x%x, mask: 0x%x with irq%d\n", priv->instance_id, irq_status, irq_raw_status, irq_mask, irq);

    if (desc->int_handle)
    {
        desc->int_handle(desc);
    }

    writel(irq_status, priv->base_addr + GPIO_PORTA_EOI_OFFSET);
}

int e2000_gpio_set_int(uintptr_t base_addr, uint32_t pin, uint32_t value)
{
    uint32_t reg_val;
    unsigned int pin_index;

    if (value != 0 && value != 1)
    {
        return -EINVAL; // 无效参数
    }

    pin_index = pin & 0x00000fffU;
    KLOG_I("LDbg{%s}[%d]. pin_idx:%u, baseaddr:%xH, val:%x\n", __func__, __LINE__, pin_index, base_addr, value);
    reg_val = readl(base_addr + GPIO_INTEN_OFFSET);
    if (value == 0)
    {
        reg_val &= ~(BIT(pin_index));
    }
    else if (value == 1)
    {
        reg_val |= (BIT(pin_index));
    }
    else
    {
        KLOG_W("ERROR, Invalid value\n");
        return -EINVAL;
    }

    writel(reg_val, base_addr + GPIO_INTEN_OFFSET);

    KLOG_I("Setting GPIO pin %u interrupt to %u\n", pin, value);
    return 0;
}

int e2000_gpio_set_int_mode(uintptr_t base_addr, uint32_t pin, enum gpio_int_mode state)
{
    uint32_t reg_int_en, reg_int_level, reg_int_mask, reg_int_polarity;
    unsigned int pin_index;

    pin_index = pin & 0x00000fffU;

    reg_int_en = readl(base_addr + GPIO_INTEN_OFFSET);
    reg_int_level = readl(base_addr + GPIO_INTTYPE_LEVEL_OFFSET);
    reg_int_mask = readl(base_addr + GPIO_INTMASK_OFFSET);
    reg_int_polarity = readl(base_addr + GPIO_INT_POLARITY_OFFSET);
    switch (state)
    {
        case GPIO_INT_MODE_DISABLE:
            reg_int_en &= ~(BIT(pin_index));
            break;
        case GPIO_INT_MODE_RISING:
            reg_int_level |= (BIT(pin_index));
            reg_int_polarity |= (BIT(pin_index));
            break;
        case GPIO_INT_MODE_FALLING:
            reg_int_level |= (BIT(pin_index));
            reg_int_polarity &= ~(BIT(pin_index));
            break;
        case GPIO_INT_MODE_LOWLEVEL:
            reg_int_level &= ~(BIT(pin_index));
            reg_int_polarity &= ~(BIT(pin_index));
            break;
        case GPIO_INT_MODE_HIGHLEVEL:
            reg_int_level &= ~(BIT(pin_index));
            reg_int_polarity |= (BIT(pin_index));
            break;
        default:
            KLOG_W("ERROR, Invalid value\n");
            return -EINVAL;
    }

    writel(reg_int_en, base_addr + GPIO_INTEN_OFFSET);
    writel(reg_int_level, base_addr + GPIO_INTTYPE_LEVEL_OFFSET);
    writel(reg_int_mask, base_addr + GPIO_INTMASK_OFFSET);
    writel(reg_int_polarity, base_addr + GPIO_INT_POLARITY_OFFSET);

    KLOG_I("Setting GPIO pin %u interrupt mode to %d\n", pin, state);
    return 0;
}

static int kgpio_int_set(struct gpio_desc *desc, uint32_t state)
{
    struct gpio_priv_s *priv;
    int start_no = (desc->parent)->start_no;
    if (desc == NULL)
    {
        KLOG_E("%s %s\n", __func__, "ERROR, Invalid parameter");
        return -1;
    }

    priv = (struct gpio_priv_s *)desc->parent->priv;

    if (state == GPIO_INT_ENABLE)
    {
        e2000_gpio_set_int(priv->base_addr, desc->gpio_id, 1);
        e2000_gpio_set_int_mode(priv->base_addr, desc->gpio_id, GPIO_INT_MODE_RISING);
        ttos_pic_irq_install (priv->irq[desc->gpio_id], handle_gpio_int, desc, 0, "gpio");
        ttos_pic_irq_unmask (priv->irq[desc->gpio_id]);
    }
    else
    {
        e2000_gpio_set_int(priv->base_addr, desc->gpio_id, 0);
        ttos_pic_irq_mask (priv->irq[desc->gpio_id]);
    }

    return 0;
}
static int kgpio_mode(struct gpio_desc *desc, uint32_t mode)
{
    struct gpio_priv_s *priv;

    if (desc == NULL)
    {
        KLOG_E("%s %s\n", __func__, "ERROR, Invalid parameter");
        return -1;
    }
    int start_no = (desc->parent)->start_no;

    priv = (struct gpio_priv_s *)desc->parent->priv;

    if (mode == GPIO_MODE_INPUT)
    {
        e2000_gpio_set_direction(priv->base_addr, desc->gpio_id, GPIO_DIRECTION_INPUT);
    }
    else if (mode == GPIO_MODE_OUTPUT)
    {
        e2000_gpio_set_direction(priv->base_addr, desc->gpio_id, GPIO_DIRECTION_OUTPUT);
    }
    else if (mode == GPIO_MODE_INT_EDGE)
    {
        e2000_gpio_set_int_mode(priv->base_addr, desc->gpio_id, GPIO_INT_MODE_RISING);
    }
    else if (mode == GPIO_MODE_INT_LEVEL)
    {
        e2000_gpio_set_int_mode(priv->base_addr, desc->gpio_id, GPIO_INT_MODE_HIGHLEVEL);
    }
    else
    {
        KLOG_W("ERROR, Invalid value\n");
        return -EINVAL;
    }

    return 0;
}

static int kgpio_read(struct gpio_desc *desc)
{
    struct gpio_priv_s *priv;
    int start_no = (desc->parent)->start_no;

    priv = (struct gpio_priv_s *)desc->parent->priv;

    return e2000_gpio_get_value(priv->base_addr, desc->gpio_id);
}

static int kgpio_write(struct gpio_desc *desc, uint32_t state)
{
    struct gpio_priv_s *priv;
    int start_no = (desc->parent)->start_no;

    priv = (struct gpio_priv_s *)desc->parent->priv;

    return e2000_gpio_set_value(priv->base_addr, desc->gpio_id, state);
}

static int fgpio_open(struct file *filep)
{
    struct gpio_priv_s *priv;
    priv = filep->f_inode->i_private;

    // Initialize GPIO hardware
    // e.g., e2000_gpio_init(priv->base_addr);

    KLOG_I("GPIO %d opened\n", priv->instance_id);

    return 0;
}

static int fgpio_close(struct file *filep) 
{
    struct gpio_priv_s *priv;
    priv = filep->f_inode->i_private;

    // Deinitialize GPIO hardware if necessary
    // e.g., e2000_gpio_deinit(priv->base_addr);

    KLOG_I("GPIO %d closed\n", priv->instance_id);

    return 0;
}

static ssize_t fgpio_read(struct file *filep, char *buffer, size_t buflen) {
    struct gpio_priv_s *priv;
    priv = filep->f_inode->i_private;

    // Implement GPIO read functionality
    // e.g., read from hardware registers

    KLOG_I("GPIO %d read\n", priv->instance_id);

    return 0;
}

static ssize_t fgpio_write(struct file *filep, const char *buffer, size_t buflen)
{
    struct gpio_priv_s *priv;
    priv = filep->f_inode->i_private;

    // Implement GPIO write functionality
    // e.g., write to hardware registers

    KLOG_I("GPIO %d write\n", priv->instance_id);

    return 0;
}

void poll_gpio_handle(struct gpio_desc *desc)
{
    struct gpio_priv_s *priv = NULL;
    uint32_t *irq_status = NULL;
    uint32_t *irq_data = NULL;

    if (desc)
    {
        priv = (struct gpio_priv_s *)(desc->parent->priv);
        if (priv)
        {
            if (priv->udata)
            {
                irq_data = (uint32_t *)priv->udata;
                /* 用户态从数组0中读取信息，读取完后往数组1中回写清中断信息 */
                irq_data[0] = (1 << desc->gpio_id) | (irq_data[0] & ~irq_data[1]);    //填写管脚号供用户态程序读取
                irq_data[1] = 0;
            }

            if (priv->aio.aio_enable)
            {
                kpoll_notify (&priv->d_fds, 1, POLLIN, &priv->aio, &priv->polllock);
            }
        }
    }
}

static int fgpio_ioctl(struct file *filep, unsigned int cmd, unsigned long arg)
{
    struct gpio_priv_s *priv;
    struct gpio_config_s *config = (struct gpio_config_s *)arg;
    struct gpio_desc *desc = NULL;
    int ret = 0;

    priv = filep->f_inode->i_private;

    switch (cmd) {
        case FIOASYNC:
            {
                priv->aio.aio_enable = !!arg;
            }
            break;
        case FIOGETOWN:
            {
                if (arg)
                {
                    *(int *)arg = priv->aio.pid;
                }
            }
            break;
        case FIOSETOWN:
            {
                if (arg)
                {
                    priv->aio.pid = (int)arg;
                }
                else
                {
                    ret = -EINVAL;
                }
            }
            break;
        case GPIO_IOC_SET_DIRECTION:
        case GPIO_IOC_GET_DIRECTION:
        case GPIO_IOC_SET_VALUE:
        case GPIO_IOC_GET_VALUE:
        case GPIO_IOC_SET_CONFIG:
        case GPIO_IOC_GET_CONFIG:
        case GPIO_IOC_SET_MUX:
        case GPIO_IOC_EN_INT:
            {
                if (config->pin >= priv->gpio_count)
                {
                    return -EINVAL;
                }
            }
            break;
        default:
            break;
    }

    switch (cmd) {
        case FIOASYNC:
        case FIOGETOWN:
        case FIOSETOWN:
            break;
        case GPIO_IOC_SET_DIRECTION:
            ret = e2000_gpio_set_direction(priv->base_addr, config->pin, config->direction);
            break;
        case GPIO_IOC_GET_DIRECTION:
            config->direction = e2000_gpio_get_direction(priv->base_addr, config->pin);
            if ((config->direction != GPIO_DIRECTION_INPUT) && (config->direction != GPIO_DIRECTION_OUTPUT))
            {
                ret = -1;
            }
            break;
        case GPIO_IOC_SET_VALUE:
            ret = e2000_gpio_set_value(priv->base_addr, config->pin, config->value);
            break;
        case GPIO_IOC_GET_VALUE:
            config->value = e2000_gpio_get_value(priv->base_addr, config->pin);
            if (config->value != 0 && config->value != 1)
            {
                ret = -1;
            }
            break;
        case GPIO_IOC_SET_CONFIG:
            FIOPadSetGpioMux(priv->instance_id, config->pin);
            e2000_gpio_set_direction(priv->base_addr, config->pin, config->direction);
            e2000_gpio_set_value(priv->base_addr, config->pin, config->value);
            break;
        case GPIO_IOC_GET_CONFIG:
            config->direction = e2000_gpio_get_direction(priv->base_addr, config->pin);
            if (config->direction < 0)
            {
                ret = -1;
            }

            config->value = e2000_gpio_get_value(priv->base_addr, config->pin);
            if (config->value < 0)
            {
                ret = -1;
            }
            break;
        case GPIO_IOC_SET_MUX:
            /* 设置GPIO复用功能 */
            FIOPadSetGpioMux(priv->instance_id, config->pin);
            break;
        case GPIO_IOC_EN_INT:
            if (config->pin >= FT_MAX_GPIO_PIN)
            {
                return -EINVAL;
            }
            desc = priv->gpio.desc + config->pin;
            gpio_int_handle_set(desc, poll_gpio_handle);
            ret = kgpio_int_set(desc, GPIO_INT_ENABLE);
            break;
        default:
            return -EINVAL;
    }

    KLOG_I("GPIO %d ioctl cmd: %d\n", priv->instance_id, cmd);

    return ret;
}

static int fgpio_poll (struct file *filep, struct kpollfd *fds, bool setup)
{
    struct gpio_priv_s *priv = NULL;
    irq_flags_t flags;
    if (NULL == filep)
    {
        return -EINVAL;
    }

    priv = filep->f_inode->i_private;

    if (NULL == priv)
    {
        return -EINVAL;
    }
    spin_lock_irqsave(&priv->polllock, flags);
    if(setup)
        priv->d_fds = fds;
    else
        priv->d_fds = NULL;
    spin_unlock_irqrestore(&priv->polllock, flags);

    return 0;
}

static int fgpio_unmap (void *task_group, struct mm_region *entry,
                         void *start, size_t length)
{
    return 0;
}

int fgpio_mmap (struct file *filep, struct mm_region *map)
{
    int len = 0x1000;
    struct mm *mm = NULL;
    struct mm_region *uregion = NULL;
    uintptr_t attr = 0;
    pcb_t pcb = NULL;
    struct gpio_priv_s *priv = NULL;

    if (NULL == filep)
    {
        return -EINVAL;
    }

    if (NULL == map)
    {
        return -EINVAL;
    }

    priv = filep->f_inode->i_private;

    if (NULL == priv->udata)
    {
        priv->udata = (void *)page_address(pages_alloc(page_bits(len), ZONE_NORMAL));
        if (NULL == priv->udata)
        {
            return -ENOMEM;
        }
        memset ((void *)priv->udata, 0, len);
    }

    map->priv.p = NULL;
    map->munmap = NULL;
    map->physical_address = mm_kernel_v2p((virt_addr_t)priv->udata);
    map->is_page_free = false;
    map->region_page_count = len >> PAGE_SIZE_SHIFT;

    return 0;
}

static int get_gpio_group_index(phys_addr_t reg_addr)
{
    int i = 0;
    int ret = -1;
    phys_addr_t addr = 0;

    if ((reg_addr >= FGPIO0_BASE_ADDR) && (reg_addr <= FGPIO5_BASE_ADDR))
    {
        addr = reg_addr - FGPIO0_BASE_ADDR;
        ret = addr / 0x1000U;
    }

    return ret;
}

static int gpio_probe(struct device *dev)
{
    int ret = 0;
    int irq_count = 0;
    struct gpio_priv_s *priv = NULL;
    struct gpio_obj *gpio = NULL;
    uint32_t value[4] = {0};
    uint32_t irqtable[48] = {0};
    uint32_t interrupts = 0;

    if (NULL == dev)
    {
        return -EINVAL;
    }

    if (gpio_controller_check(dev))
    {
        return -EINVAL;
    }

    ret = of_property_read_u32_array (dev->of_node, "reg", &value[0], 4);
    if (ret)
    {
        ret = -EINVAL;
        goto errout;
    }

    priv = calloc(1, sizeof(struct gpio_priv_s));
    if (priv == NULL)
    {
        ret = -ENOMEM;
        KLOG_E("No enough memory\n");
        goto errout;
    }

    priv->reg_addr = ((phys_addr_t)value[0] << 32) | value[1];
    priv->reg_size = value[3];
    priv->instance_id = get_gpio_group_index(priv->reg_addr);
    priv->gpio_count = 16;

    if (priv->instance_id >= MAX_FT_GPIO_CON)
    {
        ret = -ENXIO;
        goto errout;
    }

    interrupts = of_property_count_u32_elems(dev->of_node, "interrupts");

    if (interrupts > (FT_MAX_GPIO_PIN * FT_IRQ_GPIO_CELLS))
    {
        KLOG_E("interrupt num too long\n");
        ret = -E2BIG;
        goto errout;
    }

    ret = of_property_read_u32_array (dev->of_node, "interrupts", irqtable, interrupts);

    for (int i = 0; i < (interrupts / FT_IRQ_GPIO_CELLS); i++)
    {
        priv->irq[i] = ttos_pic_irq_alloc(NULL, 32 + irqtable[1 + FT_IRQ_GPIO_CELLS * i]);
    }

    snprintf(priv->name, sizeof(dev->name), "/dev/gpio-%d", priv->instance_id);

    ret = register_driver(priv->name, &g_gpio_fops, 0666, priv);
    if (ret)
    {
        KLOG_E("register driver %s failed\n", priv->name);
        goto errout;
    }

    KLOG_I("GPIO %d probed\n", priv->instance_id);

    spin_lock_init(&priv->polllock);

    gpio = &priv->gpio;
    gpio->priv = (void *)priv;
    gpio->mmio.paddr = priv->reg_addr;
    gpio->mmio.size = priv->reg_size;

    gpio->parent   = dev;
    gpio->ops      = &g_gpio_obj_ops;
    gpio->ngpio    = priv->gpio_count;
    gpio->start_no = 0;

    ret = gpio_controller_register(gpio);
    if (ret != 0)
    {
        goto errout;
    }

    priv->base_addr = gpio->mmio.vaddr;
    if ((void *)priv->base_addr == NULL)
    {
        printk("gpio_probe: reg_addr: 0x%x, base_addr: 0x%x\n", priv->reg_addr, priv->base_addr);
        goto errout;
    }

    return 0;
errout:
    if (priv)
    {
        free(priv);
    }
    return ret;
}

static struct of_device_id gpio_table[] = {
    { .compatible = "phytium,gpio" },
    { /* end of list */ },
};

static struct driver gpio_init = {
    .name = "gpio",
    .probe = gpio_probe,
    .match_table = &gpio_table[0],
};

int phytium_gpio_init(void) {
    platform_add_driver(&gpio_init);
}
INIT_EXPORT_DRIVER(phytium_gpio_init, "phytium gpio init");
