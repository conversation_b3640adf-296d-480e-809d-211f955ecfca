#include <bits/ioctl.h>
#include <driver/devbus.h>
#include <driver/device.h>
#include <driver/driver.h>
#include <driver/gpio/gpio.h>
#include <driver/of.h>
#include <errno.h>
#include <fs/fs.h>
#include <inttypes.h>
#include <io.h>
#include <stdio.h>
#include <system/bitops.h>
#include <ttos_init.h>
#include <ttos_pic.h>

#undef KLOG_TAG
#define KLOG_TAG "GPIO_KEYS"
#include <klog.h>

/* Default Number of Poll Waiters is 1 */
#ifndef CONFIG_GPIO_KEYS_NPOLLWAITERS
#define CONFIG_GPIO_KEYS_NPOLLWAITERS 1
#endif

struct gpio_keys_priv
{
    const char *label;
    struct gpio_desc *gpio;
    MUTEX_ID lock;
    size_t int_pending;
    /* Poll Waiters for device */
    struct kpollfd *fds[CONFIG_GPIO_KEYS_NPOLLWAITERS];
    ttos_spinlock_t polllock;
    struct aio_info aio;
};

static void gpio_keys_irq_isr(struct gpio_desc *gpio)
{
    struct gpio_keys_priv *priv = gpio->priv;

    priv->int_pending = true;

    kpoll_notify(priv->fds, CONFIG_GPIO_KEYS_NPOLLWAITERS, POLLIN, &priv->aio, &priv->polllock);
}

static int gpio_keys_open(struct file *filep)
{
    struct gpio_keys_priv *priv;
    T_TTOS_ReturnCode ret;

    priv = filep->f_inode->i_private;

    ret = TTOS_ObtainMutex(priv->lock, TTOS_WAIT_FOREVER);
    if (ret != TTOS_OK)
    {
        return -EAGAIN;
    }

    gpio_mode(priv->gpio, GPIO_MODE_INPUT);

    gpio_int_handle_set(priv->gpio, gpio_keys_irq_isr);

    gpio_int_enable(priv->gpio);

    TTOS_ReleaseMutex(priv->lock);

    return 0;
}

static int gpio_keys_close(struct file *filep)
{
    struct gpio_keys_priv *priv;
    T_TTOS_ReturnCode ret;

    priv = filep->f_inode->i_private;

    ret = TTOS_ObtainMutex(priv->lock, TTOS_WAIT_FOREVER);
    if (ret != TTOS_OK)
    {
        return -EAGAIN;
    }

    gpio_int_disable(priv->gpio);

    TTOS_ReleaseMutex(priv->lock);

    return 0;
}

static ssize_t gpio_keys_read(struct file *filep, char *buffer, size_t buflen)
{
    struct gpio_keys_priv *priv;
    T_TTOS_ReturnCode ret;

    priv = filep->f_inode->i_private;

    if (buffer == NULL || buflen == 0)
    {
        return 0; /* Zero will be interpreted as the End-of-File. */
    }

    ret = TTOS_ObtainMutex(priv->lock, TTOS_WAIT_FOREVER);
    if (ret != TTOS_OK)
    {
        return -EAGAIN;
    }

    buffer[0] = gpio_read(priv->gpio);
    buffer[0] += '0';
    priv->int_pending = false;

    TTOS_ReleaseMutex(priv->lock);

    return 1;
}

static int gpio_keys_ioctl(struct file *filep, unsigned int cmd, unsigned long arg)
{
    struct gpio_keys_priv *priv;
    int ret = 0;

    priv = filep->f_inode->i_private;

    switch (cmd)
    {
    case FIOASYNC:
    {
        priv->aio.aio_enable = !!arg;
    }
    break;
    case FIOGETOWN:
    {
        if (arg)
        {
            *(int *)arg = priv->aio.pid;
        }
    }
    break;
    case FIOSETOWN:
    {
        if (arg)
        {
            priv->aio.pid = (int)arg;
        }
        else
        {
            ret = -EINVAL;
        }
    }
    break;
    default:
        ret = -EINVAL;
        break;
    }
    return ret;
}

static int gpio_keys_poll(struct file *filep, struct kpollfd *fds, bool setup)
{
    struct gpio_keys_priv *priv;
    struct inode *inode;
    int ret = 0;
    int i;
    irq_flags_t flags;

    /* Some sanity checking */

#ifdef CONFIG_DEBUG_FEATURES
    if (dev == NULL || fds == NULL)
    {
        return -ENODEV;
    }
#endif

    priv = filep->f_inode->i_private;

    /* Are we setting up the poll?  Or tearing it down? */

    spin_lock_irqsave(&priv->polllock, flags);

    if (setup)
    {
        /* Find an available slot for the Poll Waiter */

        for (i = 0; i < CONFIG_GPIO_KEYS_NPOLLWAITERS; i++)
        {
            /* Find an available slot */

            if (!priv->fds[i])
            {
                /* Bind the poll structure and this slot */

                priv->fds[i] = fds;
                fds->priv = &priv->fds[i];
                break;
            }
        }

        /* Should we immediately notify on any of the requested events? */

        if (i >= CONFIG_GPIO_KEYS_NPOLLWAITERS)
        {
            fds->priv = NULL;
            ret = -EBUSY;
            goto errout;
        }
        else
        {
            /* If Interrupt Pending is set, notify the Poll Waiters */

            if (priv->int_pending)
            {
                kpoll_notify(priv->fds, CONFIG_GPIO_KEYS_NPOLLWAITERS, POLLIN, &priv->aio, NULL);
            }
        }
    }
    else if (fds->priv != NULL)
    {
        /* This is a request to tear down the poll. */

        struct kpollfd **slot = (struct kpollfd **)fds->priv;

#ifdef CONFIG_DEBUG_FEATURES
        if (!slot)
        {
            ret = -EIO;
            goto errout;
        }
#endif

        /* Remove all memory of the poll setup */

        *slot = NULL;
        fds->priv = NULL;
    }
errout:
    spin_unlock_irqrestore(&priv->polllock, flags);
    return ret;
}

static const struct file_operations g_gpio_keys_fileops = {
    .open = gpio_keys_open,
    .close = gpio_keys_close,
    .read = gpio_keys_read,
    .ioctl = gpio_keys_ioctl,
    .poll = gpio_keys_poll,
};

static const struct of_device_id gpio_keys_of_match[] = {
    {
        .compatible = "gpio-keys",
    },
    {},
};

static int gpio_keys_probe(struct device *dev)
{
    struct gpio_keys_priv *priv;
    int ret = 0;
    char *path;
    struct device_node *child;

    for_each_child_of_node(dev->of_node, child)
    {
        priv = calloc(1, sizeof(*priv));
        if (priv == NULL)
        {
            KLOG_E("No enough memory\n");
            return -ENOMEM;
        }

        priv->gpio = gpio_from_device_get(child->device, "gpios");
        of_property_read_string(child, "label", &priv->label);

        KLOG_I("gpio: %d label: %s", priv->gpio->gpio_id, priv->label);

        priv->gpio->priv = priv;

        TTOS_CreateMutex(1, 0, &priv->lock);
        spin_lock_init(&priv->polllock);

        asprintf(&path, "/dev/%s", priv->label);
        if (path == NULL)
        {
            return -ENOMEM;
        }

        ret = register_driver(path, &g_gpio_keys_fileops, 0666, priv);
        if (ret < 0)
        {
            KLOG_E("register driver %s failed\n", path);
        }

        free(path);
    }

    return ret;
}

static struct driver gpio_keys_device_driver = {
    .name = "gpio-keys",
    .probe = gpio_keys_probe,
    .match_table = gpio_keys_of_match,
};

static int gpio_keys_init(void)
{
    platform_add_driver(&gpio_keys_device_driver);
}

INIT_EXPORT_DRIVER(gpio_keys_init, "Keyboard driver for GPIOs");
