#ifndef __KPOLL_H
#define __KPOLL_H

#include <poll.h>
#include <stdbool.h>
#include <stdint.h>

#define POLLFILE 0x800

typedef struct ttos_spinlock ttos_spinlock_t;

struct kpollfd;
typedef void (*pollcb_t)(struct kpollfd *fds);

struct kpollfd
{
    /* Standard fields */
    struct pollfd pollfd;

    void *arg;
    pollcb_t cb;
    void *priv;
};

struct aio_info
{
    bool aio_enable;
    int pid;
};

int kpoll(struct kpollfd *fds, nfds_t nfds, int timeout);
void kpoll_notify (struct kpollfd **afds, int nfds, short eventset, struct aio_info* aio, ttos_spinlock_t * lock);

#define POLLMASK                                                                                   \
    (POLLIN | POLLPRI | POLLOUT | POLLERR | POLLHUP | POLLNVAL | POLLRDNORM | POLLRDBAND |         \
     POLLWRNORM | POLLWRBAND | POLLMSG | POLLRDHUP)

#define POLLALWAYS (0x10000) /* For not conflict with Linux */

#endif /* __KPOLL_H */
