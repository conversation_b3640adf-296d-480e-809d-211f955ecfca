#include <stdint.h>
#include <errno.h>
#include <list.h>
#include <assert.h>

#include <net/net.h>
#include <netinet/in.h>
#include <net/if.h>
#include <netinet/if_ether.h>
#include <netpacket/packet.h>

#include <net/netdev.h>
#include <net/ethernet_dev.h>
#include <net/packet.h>

static packet_sock_t *find_active_packet_socket(NET_DEV *ndev, uint16_t proto, uint32_t *idx)
{
    irq_flags_t pl_flags;
    packet_sock_t *target = NULL;
    uint32_t i;

    spin_lock_irqsave(&ACTIVE_PACKET_SOCKETS_LOCK, pl_flags);

    if (*idx < ACTIVE_PACKET_SOCKETS.count)
    {
        for (i = *idx; i < ACTIVE_PACKET_SOCKETS.count; i += 1)
        {
            if ((ACTIVE_PACKET_SOCKETS.socks[i]->eth == (ETH_DEV *)ndev->link_data)
                && (ACTIVE_PACKET_SOCKETS.socks[i]->proto == __htons(ETH_P_ALL) || ACTIVE_PACKET_SOCKETS.socks[i]->proto == __htons(proto)))
            {
                target = ACTIVE_PACKET_SOCKETS.socks[i];
                break;
            }
        }
    }

    spin_unlock_irqrestore(&ACTIVE_PACKET_SOCKETS_LOCK, pl_flags);

    if (NULL == target)
    {
        *idx = (uint32_t)(-1);
    }
    else
    {
        *idx = i + 1;
    }

    return target;
}

int packet_input(ETH_DEV *eth, const ETH_NETPKT *pkt_origin)
{
    packet_sock_t *priv = NULL;
    ETH_NETPKT *new_pkt = NULL;
    ETH_NETPKT *pkt = (ETH_NETPKT *)pkt_origin;
    short event_set = 0;
    irq_flags_t rw_flags;
    uint16_t proto;
    uint32_t idx = 0;
    bool multi_delivery = false;

    proto = eth_network_protocol_parse((ether_header_t *)pkt->buf);

    do
    {
        priv = find_active_packet_socket(eth->netdev, proto, &idx);

        if (priv)
        {
            if (multi_delivery)
            {
                new_pkt = eth_netpkt_clone(pkt_origin, standalone);
                if (NULL == new_pkt)
                {
                    continue;
                }

                pkt = new_pkt;
            }
            else
            {
                eth->netdev->afpkt_stats.in_pacs++;
                eth->netdev->afpkt_stats.in_bytes += pkt_origin->len;
            }

            spin_lock_irqsave(&priv->stat.rw_lock, rw_flags);

            if (MAX_PACKET_EVENT_QUEUE == priv->stat.rx_event)
            {
                spin_unlock_irqrestore(&priv->stat.rw_lock, rw_flags);
                if (multi_delivery)
                {
                    eth_netpkt_free(pkt);
                }
                continue;
            }

            if (priv->pkt_list.head && priv->pkt_list.tail)
            {
                priv->pkt_list.tail->next = pkt;
                pkt->next = NULL;
                priv->pkt_list.tail = pkt;
            }
            else if ((!priv->pkt_list.head) && (!priv->pkt_list.tail))
            {
                priv->pkt_list.head = pkt;
                priv->pkt_list.tail = pkt;
                pkt->next = NULL;
            }
            else
            {
                assert(0);
            }

            priv->stat.rx_event += 1;

            spin_unlock_irqrestore(&priv->stat.rw_lock, rw_flags);

            multi_delivery = true;

            event_set |= POLLIN;
            TTOS_ReleaseSema(priv->pkt_sem);
            kpoll_notify(&priv->stat.kfd, 1, event_set, &priv->stat.aio, &priv->poll_lock);
        }
    } while (priv != NULL);

    if (!multi_delivery)
    {
        eth->netdev->afpkt_stats.din_pacs++;
        eth->netdev->afpkt_stats.din_bytes += pkt_origin->len;
        eth_netpkt_free((ETH_NETPKT *)pkt_origin);
    }

    return 0;
}
