From fa358ce1e841eec95727b76c4a500f22afab11aa Mon Sep 17 00:00:00 2001
From: ning<PERSON><PERSON> <>
Date: Fri, 2 Aug 2024 15:21:00 +0000
Subject: [PATCH 01/16] =?UTF-8?q?=E9=80=82=E9=85=8D=E7=B3=BB=E7=BB=9F?=
 =?UTF-8?q?=E6=8E=A5=E5=8F=A3?=
MIME-Version: 1.0
Content-Type: text/plain; charset=UTF-8
Content-Transfer-Encoding: 8bit

---
 CMakeLists.txt                       | 70 ++++++++++++++++------------
 src/Filelists.cmake                  | 42 ++---------------
 src/api/netdb.c                      |  1 -
 src/api/sockets.c                    | 38 ++++++++++-----
 src/include/lwip/priv/sockets_priv.h |  1 +
 5 files changed, 73 insertions(+), 79 deletions(-)

diff --git a/CMakeLists.txt b/CMakeLists.txt
index 55fc44a1..da224cff 100644
--- a/CMakeLists.txt
+++ b/CMakeLists.txt
@@ -1,35 +1,47 @@
-cmake_minimum_required(VERSION 3.10)
+cmake_minimum_required(VERSION 3.13)
 
-set (CMAKE_CONFIGURATION_TYPES "Debug;Release")
+set(CMAKE_GENERATOR "Unix Makefiles")
 
-project(lwIP)
+set(COMPONENT_NAME   lwip)
+set(TARGET_LIB_NAME  "lib${COMPONENT_NAME}")
 
-# Example lwIP application
+project(${COMPONENT_NAME} C)
+
+add_library(${COMPONENT_NAME} STATIC)
+
+target_compile_definitions(${COMPONENT_NAME} PRIVATE -D__arm__)
+target_compile_definitions(${COMPONENT_NAME} PRIVATE -D__AARCH32__)
+target_compile_options(${COMPONENT_NAME} PRIVATE -fno-omit-frame-pointer)
+target_compile_options(${COMPONENT_NAME} PRIVATE -fno-optimize-sibling-calls)
+target_compile_options(${COMPONENT_NAME} PRIVATE -ffunction-sections)
+target_compile_options(${COMPONENT_NAME} PRIVATE -fdata-sections)
+target_compile_options(${COMPONENT_NAME} PRIVATE -fno-strict-aliasing)
+target_compile_options(${COMPONENT_NAME} PRIVATE -fno-common)
+target_compile_options(${COMPONENT_NAME} PRIVATE -fno-builtin)
+target_compile_options(${COMPONENT_NAME} PRIVATE -mno-unaligned-access)
+target_compile_options(${COMPONENT_NAME} PRIVATE -nostdinc)
+target_compile_options(${COMPONENT_NAME} PRIVATE -Werror)
+
+foreach(inc_path ${INC_PATHS})
+    target_include_directories(${COMPONENT_NAME} PRIVATE ${inc_path})
+endforeach()
+
+target_include_directories(${COMPONENT_NAME} PRIVATE ${PATCH_INC_PATH})
+
+# Install libs
+install(TARGETS ${COMPONENT_NAME} DESTINATION ${TARGET_LIB_NAME}/libs COMPONENT ${COMPONENT_NAME})
+
+# Install headers
+install(DIRECTORY   ${CMAKE_CURRENT_SOURCE_DIR}/src/include
+        DESTINATION ${TARGET_LIB_NAME}
+        COMPONENT   ${COMPONENT_NAME}
+        )
+install(DIRECTORY   ${PATCH_INC_PATH}
+        DESTINATION ${TARGET_LIB_NAME}
+        COMPONENT   ${COMPONENT_NAME}
+        )
+
+# LWIP
 set(LWIP_DIR ${CMAKE_CURRENT_SOURCE_DIR})
 
-set (LWIP_DEFINITIONS LWIP_DEBUG=1)
-
-if (${CMAKE_SYSTEM_NAME} STREQUAL "Windows")
-  add_subdirectory(${LWIP_DIR}/contrib/ports/win32/example_app)
-elseif(${CMAKE_SYSTEM_NAME} STREQUAL "Linux" OR ${CMAKE_SYSTEM_NAME} STREQUAL "Darwin")
-  add_subdirectory(${LWIP_DIR}/contrib/ports/unix/example_app)
-else()
-  message(WARNING "Host ${CMAKE_SYSTEM_NAME} is not supported to build example_app")
-endif()
-
-# Source package generation
-set(CPACK_SOURCE_GENERATOR "ZIP")
-set(CPACK_SOURCE_PACKAGE_DESCRIPTION_SUMMARY "lwIP lightweight IP stack")
-set(CPACK_PACKAGE_VERSION_MAJOR "${LWIP_VERSION_MAJOR}")
-set(CPACK_PACKAGE_VERSION_MINOR "${LWIP_VERSION_MINOR}")
-set(CPACK_PACKAGE_VERSION_PATCH "${LWIP_VERSION_REVISION}")
-set(CPACK_SOURCE_IGNORE_FILES "/build/;${CPACK_SOURCE_IGNORE_FILES};.git")
-set(CPACK_SOURCE_PACKAGE_FILE_NAME "lwip-${LWIP_VERSION_MAJOR}.${LWIP_VERSION_MINOR}.${LWIP_VERSION_REVISION}")
-include(CPack)
-
-# Generate docs before creating source package
 include(src/Filelists.cmake)
-add_custom_target(dist COMMAND ${CMAKE_MAKE_PROGRAM} package_source)
-if (TARGET lwipdocs)
-  add_dependencies(dist lwipdocs)
-endif()
diff --git a/src/Filelists.cmake b/src/Filelists.cmake
index 3a0430e4..807d7a3e 100644
--- a/src/Filelists.cmake
+++ b/src/Filelists.cmake
@@ -252,42 +252,10 @@ set(lwipallapps_SRCS
     ${lwipmqtt_SRCS}
 )
 
-# Generate lwip/init.h (version info)
-configure_file(${LWIP_DIR}/src/include/lwip/init.h.cmake.in ${LWIP_DIR}/src/include/lwip/init.h)
-
-# Documentation
-set(DOXYGEN_DIR ${LWIP_DIR}/doc/doxygen)
-set(DOXYGEN_OUTPUT_DIR output)
-set(DOXYGEN_IN  ${LWIP_DIR}/doc/doxygen/lwip.Doxyfile.cmake.in)
-set(DOXYGEN_OUT ${LWIP_DIR}/doc/doxygen/lwip.Doxyfile)
-configure_file(${DOXYGEN_IN} ${DOXYGEN_OUT})
-
-find_package(Doxygen)
-if (DOXYGEN_FOUND)
-    message(STATUS "Doxygen build started")
-
-    add_custom_target(lwipdocs
-        COMMAND ${CMAKE_COMMAND} -E remove_directory ${DOXYGEN_DIR}/${DOXYGEN_OUTPUT_DIR}/html
-        COMMAND ${DOXYGEN_EXECUTABLE} ${DOXYGEN_OUT}
-        WORKING_DIRECTORY ${DOXYGEN_DIR}
-        COMMENT "Generating API documentation with Doxygen"
-        VERBATIM)
-else (DOXYGEN_FOUND)
-    message(STATUS "Doxygen needs to be installed to generate the doxygen documentation")
-endif (DOXYGEN_FOUND)
+set(LWIP_INCLUDE_DIRS
+    ${LWIP_DIR}/src/include
+)
 
 # lwIP libraries
-add_library(lwipcore EXCLUDE_FROM_ALL ${lwipnoapps_SRCS})
-target_compile_options(lwipcore PRIVATE ${LWIP_COMPILER_FLAGS})
-target_compile_definitions(lwipcore PRIVATE ${LWIP_DEFINITIONS}  ${LWIP_MBEDTLS_DEFINITIONS})
-target_include_directories(lwipcore PRIVATE ${LWIP_INCLUDE_DIRS} ${LWIP_MBEDTLS_INCLUDE_DIRS})
-
-add_library(lwipallapps EXCLUDE_FROM_ALL ${lwipallapps_SRCS})
-target_compile_options(lwipallapps PRIVATE ${LWIP_COMPILER_FLAGS})
-target_compile_definitions(lwipallapps PRIVATE ${LWIP_DEFINITIONS}  ${LWIP_MBEDTLS_DEFINITIONS})
-target_include_directories(lwipallapps PRIVATE ${LWIP_INCLUDE_DIRS} ${LWIP_MBEDTLS_INCLUDE_DIRS})
-
-add_library(lwipmbedtls EXCLUDE_FROM_ALL ${lwipmbedtls_SRCS})
-target_compile_options(lwipmbedtls PRIVATE ${LWIP_COMPILER_FLAGS})
-target_compile_definitions(lwipmbedtls PRIVATE ${LWIP_DEFINITIONS}  ${LWIP_MBEDTLS_DEFINITIONS})
-target_include_directories(lwipmbedtls PRIVATE ${LWIP_INCLUDE_DIRS} ${LWIP_MBEDTLS_INCLUDE_DIRS})
+target_include_directories(${COMPONENT_NAME} PRIVATE ${LWIP_INCLUDE_DIRS})
+target_sources(${COMPONENT_NAME} PRIVATE ${lwipnoapps_SRCS})
diff --git a/src/api/netdb.c b/src/api/netdb.c
index e07ab049..09ee4706 100644
--- a/src/api/netdb.c
+++ b/src/api/netdb.c
@@ -393,7 +393,6 @@ lwip_getaddrinfo(const char *nodename, const char *servname,
     /* set up sockaddr */
     inet_addr_from_ip4addr(&sa4->sin_addr, ip_2_ip4(&addr));
     sa4->sin_family = AF_INET;
-    sa4->sin_len = sizeof(struct sockaddr_in);
     sa4->sin_port = lwip_htons((u16_t)port_nr);
     ai->ai_family = AF_INET;
 #endif /* LWIP_IPV4 */
diff --git a/src/api/sockets.c b/src/api/sockets.c
index b9111943..9a1f264e 100644
--- a/src/api/sockets.c
+++ b/src/api/sockets.c
@@ -135,7 +135,7 @@
       (sin)->sin_family = AF_INET; \
       (sin)->sin_port = lwip_htons((port)); \
       inet_addr_from_ip4addr(&(sin)->sin_addr, ipaddr); \
-      memset((sin)->sin_zero, 0, SIN_ZERO_LEN); }while(0)
+      }while(0)
 #define SOCKADDR4_TO_IP4ADDR_PORT(sin, ipaddr, port) do { \
     inet_addr_to_ip4addr(ip_2_ip4(ipaddr), &((sin)->sin_addr)); \
     (port) = lwip_ntohs((sin)->sin_port); }while(0)
@@ -528,6 +528,8 @@ get_socket(int fd)
   return sock;
 }
 
+struct lwip_sock * (*lwip_get_sock)(int) = get_socket;
+
 /**
  * Allocate a new socket for a given netconn.
  *
@@ -568,6 +570,7 @@ alloc_socket(struct netconn *newconn, int accepted)
        * (unless it has been created by accept()). */
       sockets[i].sendevent  = (NETCONNTYPE_GROUP(newconn->type) == NETCONN_TCP ? (accepted != 0) : 1);
       sockets[i].errevent   = 0;
+      sockets[i].kfd        = NULL;
 #endif /* LWIP_SOCKET_SELECT || LWIP_SOCKET_POLL */
       return i + LWIP_SOCKET_OFFSET;
     }
@@ -2524,6 +2527,7 @@ event_callback(struct netconn *conn, enum netconn_evt evt, u16_t len)
   int s, check_waiters;
   struct lwip_sock *sock;
   SYS_ARCH_DECL_PROTECT(lev);
+  short eventset = 0;
 
   LWIP_UNUSED_ARG(len);
 
@@ -2590,17 +2594,27 @@ event_callback(struct netconn *conn, enum netconn_evt evt, u16_t len)
       break;
   }
 
-  if (sock->select_waiting && check_waiters) {
-    /* Save which events are active */
-    int has_recvevent, has_sendevent, has_errevent;
-    has_recvevent = sock->rcvevent > 0;
-    has_sendevent = sock->sendevent != 0;
-    has_errevent = sock->errevent != 0;
-    SYS_ARCH_UNPROTECT(lev);
-    /* Check any select calls waiting on this socket */
-    select_check_waiters(s, has_recvevent, has_sendevent, has_errevent);
-  } else {
-    SYS_ARCH_UNPROTECT(lev);
+  /* Save which events are active */
+  int has_recvevent, has_sendevent, has_errevent;
+  has_recvevent = sock->rcvevent > 0;
+  has_sendevent = sock->sendevent != 0;
+  has_errevent = sock->errevent != 0;
+  if (has_recvevent)
+  {
+    eventset |= POLLIN;
+  }
+  if (has_sendevent)
+  {
+    eventset |= POLLOUT;
+  }
+  if (has_errevent)
+  {
+    eventset |= POLLERR;
+  }
+  SYS_ARCH_UNPROTECT(lev);
+
+  if (sock->kfd) {
+    kpoll_notify(&sock->kfd, 1, eventset);
   }
   done_socket(sock);
 }
diff --git a/src/include/lwip/priv/sockets_priv.h b/src/include/lwip/priv/sockets_priv.h
index c604734c..c5636cfd 100644
--- a/src/include/lwip/priv/sockets_priv.h
+++ b/src/include/lwip/priv/sockets_priv.h
@@ -89,6 +89,7 @@ struct lwip_sock {
 #define LWIP_SOCK_FD_FREE_TCP  1
 #define LWIP_SOCK_FD_FREE_FREE 2
 #endif
+  struct kpollfd *kfd;
 };
 
 #ifndef set_errno
-- 
2.34.1


From 13bcb522cb07b3da87d2161f2f6b9c9defd9d8a2 Mon Sep 17 00:00:00 2001
From: ninghaoxin <>
Date: Mon, 9 Sep 2024 19:02:04 +0000
Subject: [PATCH 02/16] =?UTF-8?q?=E6=9B=B4=E6=96=B0cmake?=
MIME-Version: 1.0
Content-Type: text/plain; charset=UTF-8
Content-Transfer-Encoding: 8bit

---
 CMakeLists.txt | 9 ++++++++-
 1 file changed, 8 insertions(+), 1 deletion(-)

diff --git a/CMakeLists.txt b/CMakeLists.txt
index da224cff..f58bcd42 100644
--- a/CMakeLists.txt
+++ b/CMakeLists.txt
@@ -9,8 +9,16 @@ project(${COMPONENT_NAME} C)
 
 add_library(${COMPONENT_NAME} STATIC)
 
+if(CONFIG_ARCH STREQUAL "aarch64")
+target_compile_definitions(${COMPONENT_NAME} PRIVATE -D__arm__)
+target_compile_definitions(${COMPONENT_NAME} PRIVATE -D__AARCH64__)
+endif(CONFIG_ARCH STREQUAL "aarch64")
+
+if(CONFIG_ARCH STREQUAL "arm")
 target_compile_definitions(${COMPONENT_NAME} PRIVATE -D__arm__)
 target_compile_definitions(${COMPONENT_NAME} PRIVATE -D__AARCH32__)
+endif(CONFIG_ARCH STREQUAL "arm")
+
 target_compile_options(${COMPONENT_NAME} PRIVATE -fno-omit-frame-pointer)
 target_compile_options(${COMPONENT_NAME} PRIVATE -fno-optimize-sibling-calls)
 target_compile_options(${COMPONENT_NAME} PRIVATE -ffunction-sections)
@@ -18,7 +26,6 @@ target_compile_options(${COMPONENT_NAME} PRIVATE -fdata-sections)
 target_compile_options(${COMPONENT_NAME} PRIVATE -fno-strict-aliasing)
 target_compile_options(${COMPONENT_NAME} PRIVATE -fno-common)
 target_compile_options(${COMPONENT_NAME} PRIVATE -fno-builtin)
-target_compile_options(${COMPONENT_NAME} PRIVATE -mno-unaligned-access)
 target_compile_options(${COMPONENT_NAME} PRIVATE -nostdinc)
 target_compile_options(${COMPONENT_NAME} PRIVATE -Werror)
 
-- 
2.34.1


From 3e19624014e63aad60873c0754617d56a8713241 Mon Sep 17 00:00:00 2001
From: ninghaoxin <>
Date: Mon, 9 Sep 2024 19:03:12 +0000
Subject: [PATCH 03/16] =?UTF-8?q?=E5=AF=B9=E4=BF=A1=E5=8F=B7=E9=80=A0?=
 =?UTF-8?q?=E6=88=90=E7=9A=84=E4=B8=AD=E6=96=AD=E5=A2=9E=E5=8A=A0=E5=A4=84?=
 =?UTF-8?q?=E7=90=86?=
MIME-Version: 1.0
Content-Type: text/plain; charset=UTF-8
Content-Transfer-Encoding: 8bit

---
 src/api/api_lib.c      | 11 ++++++++---
 src/api/err.c          |  3 ++-
 src/include/lwip/err.h |  4 +++-
 3 files changed, 13 insertions(+), 5 deletions(-)

diff --git a/src/api/api_lib.c b/src/api/api_lib.c
index 60678f88..8334d2a8 100644
--- a/src/api/api_lib.c
+++ b/src/api/api_lib.c
@@ -68,6 +68,7 @@
 #include "lwip/priv/api_msg.h"
 #include "lwip/priv/tcp_priv.h"
 #include "lwip/priv/tcpip_priv.h"
+#include "lwip/errno.h"
 
 #ifdef LWIP_HOOK_FILENAME
 #include LWIP_HOOK_FILENAME
@@ -503,14 +504,14 @@ netconn_accept(struct netconn *conn, struct netconn **new_conn)
     if (sys_arch_mbox_tryfetch(&conn->acceptmbox, &accept_ptr) == SYS_MBOX_EMPTY) {
       API_MSG_VAR_FREE_ACCEPT(msg);
       NETCONN_MBOX_WAITING_DEC(conn);
-      return ERR_WOULDBLOCK;
+      return errno == ERR_INTR ? ERR_INTR : ERR_WOULDBLOCK;
     }
   } else {
 #if LWIP_SO_RCVTIMEO
     if (sys_arch_mbox_fetch(&conn->acceptmbox, &accept_ptr, conn->recv_timeout) == SYS_ARCH_TIMEOUT) {
       API_MSG_VAR_FREE_ACCEPT(msg);
       NETCONN_MBOX_WAITING_DEC(conn);
-      return ERR_TIMEOUT;
+      return errno;
     }
 #else
     sys_arch_mbox_fetch(&conn->acceptmbox, &accept_ptr, 0);
@@ -600,6 +601,10 @@ netconn_recv_data(struct netconn *conn, void **new_buf, u8_t apiflags)
     if (sys_arch_mbox_tryfetch(&conn->recvmbox, &buf) == SYS_MBOX_EMPTY) {
       err_t err;
       NETCONN_MBOX_WAITING_DEC(conn);
+      if (errno == ERR_INTR)
+      {
+        return ERR_INTR;
+      }
       err = netconn_err(conn);
       if (err != ERR_OK) {
         /* return pending error */
@@ -614,7 +619,7 @@ netconn_recv_data(struct netconn *conn, void **new_buf, u8_t apiflags)
 #if LWIP_SO_RCVTIMEO
     if (sys_arch_mbox_fetch(&conn->recvmbox, &buf, conn->recv_timeout) == SYS_ARCH_TIMEOUT) {
       NETCONN_MBOX_WAITING_DEC(conn);
-      return ERR_TIMEOUT;
+      return errno;
     }
 #else
     sys_arch_mbox_fetch(&conn->recvmbox, &buf, 0);
diff --git a/src/api/err.c b/src/api/err.c
index dd2b62da..c22ffb5e 100644
--- a/src/api/err.c
+++ b/src/api/err.c
@@ -62,7 +62,8 @@ static const int err_to_errno_table[] = {
   ECONNABORTED,  /* ERR_ABRT       -13     Connection aborted.      */
   ECONNRESET,    /* ERR_RST        -14     Connection reset.        */
   ENOTCONN,      /* ERR_CLSD       -15     Connection closed.       */
-  EIO            /* ERR_ARG        -16     Illegal argument.        */
+  EIO,           /* ERR_ARG        -16     Illegal argument.        */
+  EINTR          /* ERR_INTR       -17     Interrupted by signal.   */
 };
 
 int
diff --git a/src/include/lwip/err.h b/src/include/lwip/err.h
index 887d9b3f..beb73edd 100644
--- a/src/include/lwip/err.h
+++ b/src/include/lwip/err.h
@@ -85,7 +85,9 @@ typedef enum {
 /** Connection closed.       */
   ERR_CLSD       = -15,
 /** Illegal argument.        */
-  ERR_ARG        = -16
+  ERR_ARG        = -16,
+/** Interrupted by signal.   */
+  ERR_INTR       = -17
 } err_enum_t;
 
 /** Define LWIP_ERR_T in cc.h if you want to use
-- 
2.34.1


From a8f548c67981d3a68042ab0e4251eb022d5b088f Mon Sep 17 00:00:00 2001
From: ninghaoxin <>
Date: Fri, 11 Oct 2024 11:01:09 +0000
Subject: [PATCH 04/16] =?UTF-8?q?=E6=9B=B4=E6=96=B0cmake?=
MIME-Version: 1.0
Content-Type: text/plain; charset=UTF-8
Content-Transfer-Encoding: 8bit

---
 CMakeLists.txt | 1 +
 1 file changed, 1 insertion(+)

diff --git a/CMakeLists.txt b/CMakeLists.txt
index f58bcd42..e1987f9f 100644
--- a/CMakeLists.txt
+++ b/CMakeLists.txt
@@ -17,6 +17,7 @@ endif(CONFIG_ARCH STREQUAL "aarch64")
 if(CONFIG_ARCH STREQUAL "arm")
 target_compile_definitions(${COMPONENT_NAME} PRIVATE -D__arm__)
 target_compile_definitions(${COMPONENT_NAME} PRIVATE -D__AARCH32__)
+target_compile_options(${COMPONENT_NAME} PRIVATE -mno-unaligned-access)
 endif(CONFIG_ARCH STREQUAL "arm")
 
 target_compile_options(${COMPONENT_NAME} PRIVATE -fno-omit-frame-pointer)
-- 
2.34.1


From 17a1a12b509343269c32ccd6ddbeb69b0d8d1ec4 Mon Sep 17 00:00:00 2001
From: zyh <<EMAIL>>
Date: Tue, 15 Oct 2024 01:43:50 +0000
Subject: [PATCH 05/16] =?UTF-8?q?=E6=B7=BB=E5=8A=A0aio?=
MIME-Version: 1.0
Content-Type: text/plain; charset=UTF-8
Content-Transfer-Encoding: 8bit

---
 CMakeLists.txt                       | 2 ++
 src/api/sockets.c                    | 5 ++---
 src/include/lwip/priv/sockets_priv.h | 2 ++
 3 files changed, 6 insertions(+), 3 deletions(-)

diff --git a/CMakeLists.txt b/CMakeLists.txt
index e1987f9f..0a5d14fb 100644
--- a/CMakeLists.txt
+++ b/CMakeLists.txt
@@ -20,6 +20,8 @@ target_compile_definitions(${COMPONENT_NAME} PRIVATE -D__AARCH32__)
 target_compile_options(${COMPONENT_NAME} PRIVATE -mno-unaligned-access)
 endif(CONFIG_ARCH STREQUAL "arm")
 
+target_compile_options(${COMPONENT_NAME} PRIVATE -g -O0)
+
 target_compile_options(${COMPONENT_NAME} PRIVATE -fno-omit-frame-pointer)
 target_compile_options(${COMPONENT_NAME} PRIVATE -fno-optimize-sibling-calls)
 target_compile_options(${COMPONENT_NAME} PRIVATE -ffunction-sections)
diff --git a/src/api/sockets.c b/src/api/sockets.c
index 9a1f264e..e88bab50 100644
--- a/src/api/sockets.c
+++ b/src/api/sockets.c
@@ -2613,9 +2613,8 @@ event_callback(struct netconn *conn, enum netconn_evt evt, u16_t len)
   }
   SYS_ARCH_UNPROTECT(lev);
 
-  if (sock->kfd) {
-    kpoll_notify(&sock->kfd, 1, eventset);
-  }
+  kpoll_notify(&sock->kfd, 1, eventset, &sock->aio);
+
   done_socket(sock);
 }
 
diff --git a/src/include/lwip/priv/sockets_priv.h b/src/include/lwip/priv/sockets_priv.h
index c5636cfd..464b3ae2 100644
--- a/src/include/lwip/priv/sockets_priv.h
+++ b/src/include/lwip/priv/sockets_priv.h
@@ -89,6 +89,8 @@ struct lwip_sock {
 #define LWIP_SOCK_FD_FREE_TCP  1
 #define LWIP_SOCK_FD_FREE_FREE 2
 #endif
+
+  struct aio_info aio;
   struct kpollfd *kfd;
 };
 
-- 
2.34.1


From 5d4664719738e4b2ed10cea9f75a076ddde2b0cc Mon Sep 17 00:00:00 2001
From: ninghaoxin <>
Date: Thu, 17 Oct 2024 15:14:11 +0000
Subject: [PATCH 06/16] =?UTF-8?q?=E6=9B=B4=E6=96=B0=E9=80=82=E9=85=8D?=
 =?UTF-8?q?=E9=9D=9E=E7=AD=89=E5=BE=85=E6=94=B6=E5=8F=91?=
MIME-Version: 1.0
Content-Type: text/plain; charset=UTF-8
Content-Transfer-Encoding: 8bit

---
 src/api/api_lib.c | 6 +-----
 1 file changed, 1 insertion(+), 5 deletions(-)

diff --git a/src/api/api_lib.c b/src/api/api_lib.c
index 8334d2a8..5a643a32 100644
--- a/src/api/api_lib.c
+++ b/src/api/api_lib.c
@@ -504,7 +504,7 @@ netconn_accept(struct netconn *conn, struct netconn **new_conn)
     if (sys_arch_mbox_tryfetch(&conn->acceptmbox, &accept_ptr) == SYS_MBOX_EMPTY) {
       API_MSG_VAR_FREE_ACCEPT(msg);
       NETCONN_MBOX_WAITING_DEC(conn);
-      return errno == ERR_INTR ? ERR_INTR : ERR_WOULDBLOCK;
+      return ERR_WOULDBLOCK;
     }
   } else {
 #if LWIP_SO_RCVTIMEO
@@ -601,10 +601,6 @@ netconn_recv_data(struct netconn *conn, void **new_buf, u8_t apiflags)
     if (sys_arch_mbox_tryfetch(&conn->recvmbox, &buf) == SYS_MBOX_EMPTY) {
       err_t err;
       NETCONN_MBOX_WAITING_DEC(conn);
-      if (errno == ERR_INTR)
-      {
-        return ERR_INTR;
-      }
       err = netconn_err(conn);
       if (err != ERR_OK) {
         /* return pending error */
-- 
2.34.1


From f78e1e6bc05a247a712e336869378b37239384d1 Mon Sep 17 00:00:00 2001
From: ninghaoxin <>
Date: Tue, 10 Dec 2024 11:44:30 +0000
Subject: [PATCH 07/16] =?UTF-8?q?=E9=80=82=E9=85=8DCORE=5FLOCKING=E5=BC=80?=
 =?UTF-8?q?=E5=90=AF=E5=90=8E=E8=A2=AB=E6=89=93=E6=96=AD=E7=9A=84=E6=83=85?=
 =?UTF-8?q?=E5=86=B5?=
MIME-Version: 1.0
Content-Type: text/plain; charset=UTF-8
Content-Transfer-Encoding: 8bit

---
 src/api/api_msg.c | 57 ++++++++++++++++++++++++++++++++++++-----------
 1 <USER> <GROUP>, 44 insertions(+), 13 deletions(-)

diff --git a/src/api/api_msg.c b/src/api/api_msg.c
index 8092be96..8811d2d5 100644
--- a/src/api/api_msg.c
+++ b/src/api/api_msg.c
@@ -47,6 +47,7 @@
 #include "lwip/udp.h"
 #include "lwip/tcp.h"
 #include "lwip/raw.h"
+#include "lwip/errno.h"
 
 #include "lwip/memp.h"
 #include "lwip/igmp.h"
@@ -756,7 +757,7 @@ netconn_alloc(enum netconn_type t, netconn_callback callback)
     sys_mbox_free(&conn->recvmbox);
     goto free_and_return;
   }
-#endif
+  #endif
 
 #if LWIP_TCP
   sys_mbox_set_invalid(&conn->acceptmbox);
@@ -1181,9 +1182,16 @@ lwip_netconn_do_delconn(void *m)
           if (lwip_netconn_do_close_internal(msg->conn, 0) != ERR_OK) {
             LWIP_ASSERT("state!", msg->conn->state == NETCONN_CLOSE);
             UNLOCK_TCPIP_CORE();
-            sys_arch_sem_wait(LWIP_API_MSG_SEM(msg), 0);
-            LOCK_TCPIP_CORE();
-            LWIP_ASSERT("state!", msg->conn->state == NETCONN_NONE);
+            if (sys_arch_sem_wait(LWIP_API_MSG_SEM(msg), 0) == SYS_ARCH_TIMEOUT && errno == ERR_INTR)
+            {
+              msg->err = ERR_INTR;
+              LOCK_TCPIP_CORE();
+            }
+            else
+            {
+              LOCK_TCPIP_CORE();
+              LWIP_ASSERT("state!", msg->conn->state == NETCONN_NONE);
+            }
           }
 #else /* LWIP_TCPIP_CORE_LOCKING */
           lwip_netconn_do_close_internal(msg->conn);
@@ -1395,9 +1403,18 @@ lwip_netconn_do_connect(void *m)
 #if LWIP_TCPIP_CORE_LOCKING
               LWIP_ASSERT("state!", msg->conn->state == NETCONN_CONNECT);
               UNLOCK_TCPIP_CORE();
-              sys_arch_sem_wait(LWIP_API_MSG_SEM(msg), 0);
-              LOCK_TCPIP_CORE();
-              LWIP_ASSERT("state!", msg->conn->state != NETCONN_CONNECT);
+              if (sys_arch_sem_wait(LWIP_API_MSG_SEM(msg), 0) == SYS_ARCH_TIMEOUT && errno == ERR_INTR)
+              {
+                msg->err = ERR_INTR;
+                msg->conn->state = NETCONN_NONE;
+                msg->conn->current_msg = NULL;
+                LOCK_TCPIP_CORE();
+              }
+              else
+              {
+                LOCK_TCPIP_CORE();
+                LWIP_ASSERT("state!", msg->conn->state != NETCONN_CONNECT);
+              }
 #endif /* LWIP_TCPIP_CORE_LOCKING */
               return;
             }
@@ -1835,9 +1852,16 @@ lwip_netconn_do_write(void *m)
         if (lwip_netconn_do_writemore(msg->conn, 0) != ERR_OK) {
           LWIP_ASSERT("state!", msg->conn->state == NETCONN_WRITE);
           UNLOCK_TCPIP_CORE();
-          sys_arch_sem_wait(LWIP_API_MSG_SEM(msg), 0);
-          LOCK_TCPIP_CORE();
-          LWIP_ASSERT("state!", msg->conn->state != NETCONN_WRITE);
+          if (sys_arch_sem_wait(LWIP_API_MSG_SEM(msg), 0) == SYS_ARCH_TIMEOUT && errno == ERR_INTR)
+          {
+            msg->err = ERR_INTR;
+            LOCK_TCPIP_CORE();
+          }
+          else
+          {
+            LOCK_TCPIP_CORE();
+            LWIP_ASSERT("state!", msg->conn->state != NETCONN_WRITE);
+          }
         }
 #else /* LWIP_TCPIP_CORE_LOCKING */
         lwip_netconn_do_writemore(msg->conn);
@@ -1989,9 +2013,16 @@ lwip_netconn_do_close(void *m)
       if (lwip_netconn_do_close_internal(msg->conn, 0) != ERR_OK) {
         LWIP_ASSERT("state!", msg->conn->state == NETCONN_CLOSE);
         UNLOCK_TCPIP_CORE();
-        sys_arch_sem_wait(LWIP_API_MSG_SEM(msg), 0);
-        LOCK_TCPIP_CORE();
-        LWIP_ASSERT("state!", msg->conn->state == NETCONN_NONE);
+        if (sys_arch_sem_wait(LWIP_API_MSG_SEM(msg), 0) == SYS_ARCH_TIMEOUT && errno == ERR_INTR)
+        {
+          msg->err = ERR_INTR;
+          LOCK_TCPIP_CORE();
+        }
+        else
+        {
+          LOCK_TCPIP_CORE();
+          LWIP_ASSERT("state!", msg->conn->state == NETCONN_NONE);
+        }
       }
 #else /* LWIP_TCPIP_CORE_LOCKING */
       lwip_netconn_do_close_internal(msg->conn);
-- 
2.34.1


From f110ca1d0b64afa1ac6bb600a1f4566b88073265 Mon Sep 17 00:00:00 2001
From: ninghaoxin <>
Date: Thu, 12 Dec 2024 16:16:42 +0000
Subject: [PATCH 08/16] =?UTF-8?q?=E4=BF=AE=E5=A4=8D=E4=BA=8B=E4=BB=B6?=
 =?UTF-8?q?=E8=AE=A1=E6=95=B0=EF=BC=88=E5=9F=BA=E4=BA=8E=E5=AD=97=E8=8A=82?=
 =?UTF-8?q?=E6=95=B0=EF=BC=89?=
MIME-Version: 1.0
Content-Type: text/plain; charset=UTF-8
Content-Transfer-Encoding: 8bit

---
 src/api/api_lib.c                    |  6 +--
 src/api/api_msg.c                    | 15 +++++---
 src/api/sockets.c                    | 55 +++++++++++++++++-----------
 src/include/lwip/api.h               |  8 ++++
 src/include/lwip/priv/sockets_priv.h | 16 ++++----
 5 files changed, 61 insertions(+), 39 deletions(-)

diff --git a/src/api/api_lib.c b/src/api/api_lib.c
index 5a643a32..2e74cc08 100644
--- a/src/api/api_lib.c
+++ b/src/api/api_lib.c
@@ -529,7 +529,7 @@ netconn_accept(struct netconn *conn, struct netconn **new_conn)
 #endif
 
   /* Register event with callback */
-  API_EVENT(conn, NETCONN_EVT_RCVMINUS, 0);
+  API_EVENT(conn, NETCONN_EVT_RCVMINUS, 1);
 
   if (lwip_netconn_is_err_msg(accept_ptr, &err)) {
     /* a connection has been aborted: e.g. out of pcbs or out of netconns during accept */
@@ -664,7 +664,7 @@ netconn_recv_data(struct netconn *conn, void **new_buf, u8_t apiflags)
   SYS_ARCH_DEC(conn->recv_avail, len);
 #endif /* LWIP_SO_RCVBUF */
   /* Register event with callback */
-  API_EVENT(conn, NETCONN_EVT_RCVMINUS, len);
+  // API_EVENT(conn, NETCONN_EVT_RCVMINUS, len);
 
   LWIP_DEBUGF(API_LIB_DEBUG, ("netconn_recv_data: received %p, len=%"U16_F"\n", buf, len));
 
@@ -751,7 +751,7 @@ netconn_recv_data_tcp(struct netconn *conn, struct pbuf **new_buf, u8_t apiflags
       return ERR_WOULDBLOCK;
     } else {
 handle_fin:
-      API_EVENT(conn, NETCONN_EVT_RCVMINUS, 0);
+      API_EVENT(conn, NETCONN_EVT_RCVMINUS, 1);
       if (conn->pcb.ip == NULL) {
         /* race condition: RST during recv */
         err = netconn_err(conn);
diff --git a/src/api/api_msg.c b/src/api/api_msg.c
index 8811d2d5..0ea92a45 100644
--- a/src/api/api_msg.c
+++ b/src/api/api_msg.c
@@ -462,7 +462,7 @@ err_tcp(void *arg, err_t err)
   API_EVENT(conn, NETCONN_EVT_ERROR, 0);
   /* Try to release selects pending on 'read' or 'write', too.
      They will get an error if they actually try to read or write. */
-  API_EVENT(conn, NETCONN_EVT_RCVPLUS, 0);
+  API_EVENT(conn, NETCONN_EVT_RCVPLUS, 1);
   API_EVENT(conn, NETCONN_EVT_SENDPLUS, 0);
 
   mbox_msg = lwip_netconn_err_to_msg(err);
@@ -551,7 +551,7 @@ accept_function(void *arg, struct tcp_pcb *newpcb, err_t err)
     /* out-of-pcbs during connect: pass on this error to the application */
     if (sys_mbox_trypost(&conn->acceptmbox, lwip_netconn_err_to_msg(ERR_ABRT)) == ERR_OK) {
       /* Register event with callback */
-      API_EVENT(conn, NETCONN_EVT_RCVPLUS, 0);
+      API_EVENT(conn, NETCONN_EVT_RCVPLUS, 1);
     }
     return ERR_VAL;
   }
@@ -567,7 +567,7 @@ accept_function(void *arg, struct tcp_pcb *newpcb, err_t err)
     /* outof netconns: pass on this error to the application */
     if (sys_mbox_trypost(&conn->acceptmbox, lwip_netconn_err_to_msg(ERR_ABRT)) == ERR_OK) {
       /* Register event with callback */
-      API_EVENT(conn, NETCONN_EVT_RCVPLUS, 0);
+      API_EVENT(conn, NETCONN_EVT_RCVPLUS, 1);
     }
     return ERR_MEM;
   }
@@ -596,7 +596,7 @@ accept_function(void *arg, struct tcp_pcb *newpcb, err_t err)
     return ERR_MEM;
   } else {
     /* Register event with callback */
-    API_EVENT(conn, NETCONN_EVT_RCVPLUS, 0);
+    API_EVENT(conn, NETCONN_EVT_RCVPLUS, 1);
   }
 
   return ERR_OK;
@@ -720,6 +720,9 @@ netconn_alloc(enum netconn_type t, netconn_callback callback)
   conn->pending_err = ERR_OK;
   conn->type = t;
   conn->pcb.tcp = NULL;
+  conn->events.rcvevent = 0;
+  conn->events.sendevent = 0;
+  conn->events.errevent = 0;
 #if LWIP_NETCONN_FULLDUPLEX
   conn->mbox_threads_waiting = 0;
 #endif
@@ -1073,7 +1076,7 @@ lwip_netconn_do_close_internal(struct netconn *conn  WRITE_DELAYED_PARAM)
         API_EVENT(conn, NETCONN_EVT_ERROR, 0);
       }
       if (shut_rx) {
-        API_EVENT(conn, NETCONN_EVT_RCVPLUS, 0);
+        API_EVENT(conn, NETCONN_EVT_RCVPLUS, 1);
       }
       if (shut_tx) {
         API_EVENT(conn, NETCONN_EVT_SENDPLUS, 0);
@@ -1209,7 +1212,7 @@ lwip_netconn_do_delconn(void *m)
 
     /* @todo: this lets select make the socket readable and writable,
        which is wrong! errfd instead? */
-    API_EVENT(msg->conn, NETCONN_EVT_RCVPLUS, 0);
+    API_EVENT(msg->conn, NETCONN_EVT_RCVPLUS, 1);
     API_EVENT(msg->conn, NETCONN_EVT_SENDPLUS, 0);
   }
   if (sys_sem_valid(LWIP_API_MSG_SEM(msg))) {
diff --git a/src/api/sockets.c b/src/api/sockets.c
index e88bab50..fc2d5948 100644
--- a/src/api/sockets.c
+++ b/src/api/sockets.c
@@ -565,11 +565,11 @@ alloc_socket(struct netconn *newconn, int accepted)
       sockets[i].lastdata.pbuf = NULL;
 #if LWIP_SOCKET_SELECT || LWIP_SOCKET_POLL
       LWIP_ASSERT("sockets[i].select_waiting == 0", sockets[i].select_waiting == 0);
-      sockets[i].rcvevent   = 0;
+      // sockets[i].conn->events.rcvevent   = 0;
       /* TCP sendbuf is empty, but the socket is not yet writable until connected
        * (unless it has been created by accept()). */
-      sockets[i].sendevent  = (NETCONNTYPE_GROUP(newconn->type) == NETCONN_TCP ? (accepted != 0) : 1);
-      sockets[i].errevent   = 0;
+      sockets[i].conn->events.sendevent  = (NETCONNTYPE_GROUP(newconn->type) == NETCONN_TCP ? (accepted != 0) : 1);
+      sockets[i].conn->events.errevent   = 0;
       sockets[i].kfd        = NULL;
 #endif /* LWIP_SOCKET_SELECT || LWIP_SOCKET_POLL */
       return i + LWIP_SOCKET_OFFSET;
@@ -716,7 +716,7 @@ lwip_accept(int s, struct sockaddr *addr, socklen_t *addrlen)
     LOCK_TCPIP_CORE();
     while (recvevent > 0) {
       recvevent--;
-      newconn->callback(newconn, NETCONN_EVT_RCVPLUS, 0);
+      newconn->callback(newconn, NETCONN_EVT_RCVPLUS, 1);
     }
     UNLOCK_TCPIP_CORE();
   }
@@ -1050,6 +1050,10 @@ lwip_recv_tcp(struct lwip_sock *sock, void *mem, size_t len, int flags)
     /* @todo: do we need to support peeking more than one pbuf? */
   } while ((recv_left > 0) && !(flags & MSG_PEEK));
 lwip_recv_tcp_done:
+  if (recvd > 0)
+  {
+    API_EVENT(sock->conn, NETCONN_EVT_RCVMINUS, recvd);
+  }
   if ((recvd > 0) && !(flags & MSG_PEEK)) {
     /* ensure window update after copying all data */
     netconn_tcp_recvd(sock->conn, (size_t)recvd);
@@ -1230,6 +1234,10 @@ lwip_recvfrom_udp_raw(struct lwip_sock *sock, int flags, struct msghdr *msg, u16
     sock->lastdata.netbuf = NULL;
     netbuf_delete(buf);
   }
+  if (buflen > 0)
+  {
+    API_EVENT(sock->conn, NETCONN_EVT_RCVMINUS, buflen);
+  }
   if (datagram_len) {
     *datagram_len = buflen;
   }
@@ -1899,9 +1907,9 @@ lwip_selscan(int maxfdp1, fd_set *readset_in, fd_set *writeset_in, fd_set *excep
     sock = tryget_socket_unconn_locked(i);
     if (sock != NULL) {
       void *lastdata = sock->lastdata.pbuf;
-      s16_t rcvevent = sock->rcvevent;
-      u16_t sendevent = sock->sendevent;
-      u16_t errevent = sock->errevent;
+      s16_t rcvevent = sock->conn->events.rcvevent;
+      u16_t sendevent = sock->conn->events.sendevent;
+      u16_t errevent = sock->conn->events.errevent;
       SYS_ARCH_UNPROTECT(lev);
 
       /* ... then examine it: */
@@ -2261,9 +2269,9 @@ lwip_pollscan(struct pollfd *fds, nfds_t nfds, enum lwip_pollscan_opts opts)
       sock = tryget_socket_unconn_locked(fds[fdi].fd);
       if (sock != NULL) {
         void* lastdata = sock->lastdata.pbuf;
-        s16_t rcvevent = sock->rcvevent;
-        u16_t sendevent = sock->sendevent;
-        u16_t errevent = sock->errevent;
+        s16_t rcvevent = sock->conn->events.rcvevent;
+        u16_t sendevent = sock->conn->events.sendevent;
+        u16_t errevent = sock->conn->events.errevent;
 
         if ((opts & LWIP_POLLSCAN_INC_WAIT) != 0) {
           sock->select_waiting++;
@@ -2529,7 +2537,7 @@ event_callback(struct netconn *conn, enum netconn_evt evt, u16_t len)
   SYS_ARCH_DECL_PROTECT(lev);
   short eventset = 0;
 
-  LWIP_UNUSED_ARG(len);
+  // LWIP_UNUSED_ARG(len);
 
   /* Get socket */
   if (conn) {
@@ -2545,7 +2553,8 @@ event_callback(struct netconn *conn, enum netconn_evt evt, u16_t len)
         if (evt == NETCONN_EVT_RCVPLUS) {
           /* conn->socket is -1 on initialization
              lwip_accept adjusts sock->recvevent if conn->socket < -1 */
-          conn->callback_arg.socket--;
+          // conn->callback_arg.socket--;
+          conn->events.rcvevent += len;
         }
         SYS_ARCH_UNPROTECT(lev);
         return;
@@ -2567,27 +2576,29 @@ event_callback(struct netconn *conn, enum netconn_evt evt, u16_t len)
   /* Set event as required */
   switch (evt) {
     case NETCONN_EVT_RCVPLUS:
-      sock->rcvevent++;
-      if (sock->rcvevent > 1) {
+      // sock->rcvevent++;
+      conn->events.rcvevent += len;
+      if (conn->events.rcvevent > 1) {
         check_waiters = 0;
       }
       break;
     case NETCONN_EVT_RCVMINUS:
-      sock->rcvevent--;
+      // sock->rcvevent--;
+      conn->events.rcvevent -= len;
       check_waiters = 0;
       break;
     case NETCONN_EVT_SENDPLUS:
-      if (sock->sendevent) {
+      if (conn->events.sendevent) {
         check_waiters = 0;
       }
-      sock->sendevent = 1;
+      conn->events.sendevent = 1;
       break;
     case NETCONN_EVT_SENDMINUS:
-      sock->sendevent = 0;
+      conn->events.sendevent = 0;
       check_waiters = 0;
       break;
     case NETCONN_EVT_ERROR:
-      sock->errevent = 1;
+      conn->events.errevent = 1;
       break;
     default:
       LWIP_ASSERT("unknown event", 0);
@@ -2596,9 +2607,9 @@ event_callback(struct netconn *conn, enum netconn_evt evt, u16_t len)
 
   /* Save which events are active */
   int has_recvevent, has_sendevent, has_errevent;
-  has_recvevent = sock->rcvevent > 0;
-  has_sendevent = sock->sendevent != 0;
-  has_errevent = sock->errevent != 0;
+  has_recvevent = conn->events.rcvevent > 0;
+  has_sendevent = conn->events.sendevent > 0;
+  has_errevent = conn->events.errevent > 0;
   if (has_recvevent)
   {
     eventset |= POLLIN;
diff --git a/src/include/lwip/api.h b/src/include/lwip/api.h
index be8c22aa..69bda145 100644
--- a/src/include/lwip/api.h
+++ b/src/include/lwip/api.h
@@ -227,6 +227,14 @@ struct netconn {
     struct udp_pcb *udp;
     struct raw_pcb *raw;
   } pcb;
+  struct {
+    s16_t rcvevent;
+    /** number of times data was ACKed (free send buffer), set by event_callback(),
+        tested by select */
+    u16_t sendevent;
+    /** error happened for this socket, set by event_callback(), tested by select */
+    u16_t errevent;
+  } events;
   /** the last asynchronous unreported error this netconn had */
   err_t pending_err;
 #if !LWIP_NETCONN_SEM_PER_THREAD
diff --git a/src/include/lwip/priv/sockets_priv.h b/src/include/lwip/priv/sockets_priv.h
index 464b3ae2..e9e48d13 100644
--- a/src/include/lwip/priv/sockets_priv.h
+++ b/src/include/lwip/priv/sockets_priv.h
@@ -70,14 +70,14 @@ struct lwip_sock {
   /** data that was left from the previous read */
   union lwip_sock_lastdata lastdata;
 #if LWIP_SOCKET_SELECT || LWIP_SOCKET_POLL
-  /** number of times data was received, set by event_callback(),
-      tested by the receive and select functions */
-  s16_t rcvevent;
-  /** number of times data was ACKed (free send buffer), set by event_callback(),
-      tested by select */
-  u16_t sendevent;
-  /** error happened for this socket, set by event_callback(), tested by select */
-  u16_t errevent;
+  // /** number of times data was received, set by event_callback(),
+  //     tested by the receive and select functions */
+  // s16_t rcvevent;
+  // /** number of times data was ACKed (free send buffer), set by event_callback(),
+  //     tested by select */
+  // u16_t sendevent;
+  // /** error happened for this socket, set by event_callback(), tested by select */
+  // u16_t errevent;
   /** counter of how many threads are waiting for this socket using select */
   SELWAIT_T select_waiting;
 #endif /* LWIP_SOCKET_SELECT || LWIP_SOCKET_POLL */
-- 
2.34.1


From 4d2ebb900e951c054dcafcc7d2cf20e651a3e067 Mon Sep 17 00:00:00 2001
From: sunxiao <<EMAIL>>
Date: Thu, 26 Dec 2024 06:28:31 +0000
Subject: [PATCH 09/16] =?UTF-8?q?DNS=E6=9B=B4=E6=96=B0=E5=90=8E=E8=B0=83?=
 =?UTF-8?q?=E7=94=A8=E7=BD=91=E7=BB=9C=E4=B8=AD=E9=97=B4=E5=B1=82=E7=9A=84?=
 =?UTF-8?q?HOOK=E5=87=BD=E6=95=B0eth=5Flwip=5Fdns=5Fupdate=5Fhook()?=
MIME-Version: 1.0
Content-Type: text/plain; charset=UTF-8
Content-Transfer-Encoding: 8bit

---
 src/core/ipv4/dhcp.c | 4 ++++
 1 file changed, 4 insertions(+)

diff --git a/src/core/ipv4/dhcp.c b/src/core/ipv4/dhcp.c
index d9ed8b0f..7f88b909 100644
--- a/src/core/ipv4/dhcp.c
+++ b/src/core/ipv4/dhcp.c
@@ -268,6 +268,8 @@ static u16_t dhcp_option_hostname(u16_t options_out_len, u8_t *options, struct n
 /* always add the DHCP options trailer to end and pad */
 static void dhcp_option_trailer(u16_t options_out_len, u8_t *options, struct pbuf *p_out);
 
+extern void eth_lwip_dns_update_hook();
+
 /** Ensure DHCP PCB is allocated and bound */
 static err_t
 dhcp_inc_pcb_refcount(void)
@@ -742,6 +744,8 @@ dhcp_handle_ack(struct netif *netif, struct dhcp_msg *msg_in)
     ip_addr_set_ip4_u32_val(dns_addr, lwip_htonl(dhcp_get_option_value(dhcp, DHCP_OPTION_IDX_DNS_SERVER + n)));
     dns_setserver(n, &dns_addr);
   }
+
+  eth_lwip_dns_update_hook ();
 #endif /* LWIP_DHCP_PROVIDE_DNS_SERVERS */
 }
 
-- 
2.34.1


From dc85ad5f055a94cb9fb88d3a4a17b58bd39dc638 Mon Sep 17 00:00:00 2001
From: ninghaoxin <>
Date: Thu, 26 Dec 2024 11:17:19 +0000
Subject: [PATCH 10/16] =?UTF-8?q?=E4=BF=AE=E6=94=B9=E4=B8=BA=E5=8F=AA?=
 =?UTF-8?q?=E6=9C=89connnect=E4=B8=AD=E7=9A=84=E7=AD=89=E4=BF=A1=E5=8F=B7?=
 =?UTF-8?q?=E9=87=8F=E5=8F=AF=E8=A2=AB=E6=89=93=E6=96=AD=EF=BC=8C=E4=BF=AE?=
 =?UTF-8?q?=E5=A4=8D=E6=9F=90=E4=BA=9B=E6=83=85=E5=86=B5=E4=B8=8B=E8=A2=AB?=
 =?UTF-8?q?=E6=89=93=E6=96=AD=E5=90=8E=E6=B5=81=E7=A8=8B=E5=87=BA=E9=94=99?=
MIME-Version: 1.0
Content-Type: text/plain; charset=UTF-8
Content-Transfer-Encoding: 8bit

---
 src/api/api_msg.c      | 53 ++++++++++++++----------------------------
 src/api/err.c          |  3 ++-
 src/include/lwip/err.h |  4 ++--
 src/include/lwip/sys.h |  9 +++++++
 4 files changed, 31 insertions(+), 38 deletions(-)

diff --git a/src/api/api_msg.c b/src/api/api_msg.c
index 0ea92a45..8ae21520 100644
--- a/src/api/api_msg.c
+++ b/src/api/api_msg.c
@@ -46,6 +46,7 @@
 #include "lwip/ip_addr.h"
 #include "lwip/udp.h"
 #include "lwip/tcp.h"
+#include "lwip/priv/tcp_priv.h"
 #include "lwip/raw.h"
 #include "lwip/errno.h"
 
@@ -760,7 +761,7 @@ netconn_alloc(enum netconn_type t, netconn_callback callback)
     sys_mbox_free(&conn->recvmbox);
     goto free_and_return;
   }
-  #endif
+#endif
 
 #if LWIP_TCP
   sys_mbox_set_invalid(&conn->acceptmbox);
@@ -1185,16 +1186,9 @@ lwip_netconn_do_delconn(void *m)
           if (lwip_netconn_do_close_internal(msg->conn, 0) != ERR_OK) {
             LWIP_ASSERT("state!", msg->conn->state == NETCONN_CLOSE);
             UNLOCK_TCPIP_CORE();
-            if (sys_arch_sem_wait(LWIP_API_MSG_SEM(msg), 0) == SYS_ARCH_TIMEOUT && errno == ERR_INTR)
-            {
-              msg->err = ERR_INTR;
-              LOCK_TCPIP_CORE();
-            }
-            else
-            {
-              LOCK_TCPIP_CORE();
-              LWIP_ASSERT("state!", msg->conn->state == NETCONN_NONE);
-            }
+            sys_arch_sem_wait_uninterruptable(LWIP_API_MSG_SEM(msg), 0);
+            LOCK_TCPIP_CORE();
+            LWIP_ASSERT("state!", msg->conn->state == NETCONN_NONE);
           }
 #else /* LWIP_TCPIP_CORE_LOCKING */
           lwip_netconn_do_close_internal(msg->conn);
@@ -1406,17 +1400,20 @@ lwip_netconn_do_connect(void *m)
 #if LWIP_TCPIP_CORE_LOCKING
               LWIP_ASSERT("state!", msg->conn->state == NETCONN_CONNECT);
               UNLOCK_TCPIP_CORE();
-              if (sys_arch_sem_wait(LWIP_API_MSG_SEM(msg), 0) == SYS_ARCH_TIMEOUT && errno == ERR_INTR)
+              if (sys_arch_sem_wait(LWIP_API_MSG_SEM(msg), 0) == SYS_ARCH_TIMEOUT && errno == ERR_RESTARTSYS)
               {
-                msg->err = ERR_INTR;
+                tcp_pcb_purge(msg->conn->pcb.tcp);
+                TCP_RMV_ACTIVE(msg->conn->pcb.tcp);
+                msg->conn->pcb.tcp->state = CLOSED;
+                msg->err = ERR_RESTARTSYS;
                 msg->conn->state = NETCONN_NONE;
                 msg->conn->current_msg = NULL;
                 LOCK_TCPIP_CORE();
               }
               else
               {
-                LOCK_TCPIP_CORE();
-                LWIP_ASSERT("state!", msg->conn->state != NETCONN_CONNECT);
+              LOCK_TCPIP_CORE();
+              LWIP_ASSERT("state!", msg->conn->state != NETCONN_CONNECT);
               }
 #endif /* LWIP_TCPIP_CORE_LOCKING */
               return;
@@ -1855,16 +1852,9 @@ lwip_netconn_do_write(void *m)
         if (lwip_netconn_do_writemore(msg->conn, 0) != ERR_OK) {
           LWIP_ASSERT("state!", msg->conn->state == NETCONN_WRITE);
           UNLOCK_TCPIP_CORE();
-          if (sys_arch_sem_wait(LWIP_API_MSG_SEM(msg), 0) == SYS_ARCH_TIMEOUT && errno == ERR_INTR)
-          {
-            msg->err = ERR_INTR;
-            LOCK_TCPIP_CORE();
-          }
-          else
-          {
-            LOCK_TCPIP_CORE();
-            LWIP_ASSERT("state!", msg->conn->state != NETCONN_WRITE);
-          }
+          sys_arch_sem_wait_uninterruptable(LWIP_API_MSG_SEM(msg), 0);
+          LOCK_TCPIP_CORE();
+          LWIP_ASSERT("state!", msg->conn->state != NETCONN_WRITE);
         }
 #else /* LWIP_TCPIP_CORE_LOCKING */
         lwip_netconn_do_writemore(msg->conn);
@@ -2016,16 +2006,9 @@ lwip_netconn_do_close(void *m)
       if (lwip_netconn_do_close_internal(msg->conn, 0) != ERR_OK) {
         LWIP_ASSERT("state!", msg->conn->state == NETCONN_CLOSE);
         UNLOCK_TCPIP_CORE();
-        if (sys_arch_sem_wait(LWIP_API_MSG_SEM(msg), 0) == SYS_ARCH_TIMEOUT && errno == ERR_INTR)
-        {
-          msg->err = ERR_INTR;
-          LOCK_TCPIP_CORE();
-        }
-        else
-        {
-          LOCK_TCPIP_CORE();
-          LWIP_ASSERT("state!", msg->conn->state == NETCONN_NONE);
-        }
+        sys_arch_sem_wait_uninterruptable(LWIP_API_MSG_SEM(msg), 0);
+        LOCK_TCPIP_CORE();
+        LWIP_ASSERT("state!", msg->conn->state == NETCONN_NONE);
       }
 #else /* LWIP_TCPIP_CORE_LOCKING */
       lwip_netconn_do_close_internal(msg->conn);
diff --git a/src/api/err.c b/src/api/err.c
index c22ffb5e..0a333b3b 100644
--- a/src/api/err.c
+++ b/src/api/err.c
@@ -63,7 +63,8 @@ static const int err_to_errno_table[] = {
   ECONNRESET,    /* ERR_RST        -14     Connection reset.        */
   ENOTCONN,      /* ERR_CLSD       -15     Connection closed.       */
   EIO,           /* ERR_ARG        -16     Illegal argument.        */
-  EINTR          /* ERR_INTR       -17     Interrupted by signal.   */
+  ERESTARTSYS,   /* ERESTARTSYS    -17     Interrupted by signal.
+                                           (may restart)            */
 };
 
 int
diff --git a/src/include/lwip/err.h b/src/include/lwip/err.h
index beb73edd..fd28f67a 100644
--- a/src/include/lwip/err.h
+++ b/src/include/lwip/err.h
@@ -86,8 +86,8 @@ typedef enum {
   ERR_CLSD       = -15,
 /** Illegal argument.        */
   ERR_ARG        = -16,
-/** Interrupted by signal.   */
-  ERR_INTR       = -17
+/** Interrupted by signal (may restart).   */
+  ERR_RESTARTSYS = -17,
 } err_enum_t;
 
 /** Define LWIP_ERR_T in cc.h if you want to use
diff --git a/src/include/lwip/sys.h b/src/include/lwip/sys.h
index 4bfdb13d..458fc36b 100644
--- a/src/include/lwip/sys.h
+++ b/src/include/lwip/sys.h
@@ -217,6 +217,15 @@ void sys_sem_signal(sys_sem_t *sem);
  * @return SYS_ARCH_TIMEOUT on timeout, any other value on success
  */
 u32_t sys_arch_sem_wait(sys_sem_t *sem, u32_t timeout);
+/**
+ * @ingroup sys_sem
+ * Additional uninterruptable sem wait
+ *
+ * @param sem the semaphore to wait for
+ * @param timeout timeout in milliseconds to wait (0 = wait forever)
+ * @return SYS_ARCH_TIMEOUT on timeout, any other value on success
+ */
+u32_t sys_arch_sem_wait_uninterruptable(sys_sem_t *sem, u32_t timeout);
 /**
  * @ingroup sys_sem
  * Deallocates a semaphore.
-- 
2.34.1


From bf7324bed31273ba5e66ec7b050bcaf8325736bf Mon Sep 17 00:00:00 2001
From: ninghaoxin <>
Date: Mon, 6 Jan 2025 15:39:57 +0000
Subject: [PATCH 11/16] =?UTF-8?q?=E5=A2=9E=E5=8A=A0CMAKE=E9=80=89=E9=A1=B9?=
 =?UTF-8?q?=EF=BC=8C=E5=8F=AF=E4=BD=BF=E7=94=A8=E5=86=85=E6=A0=B8=E9=85=8D?=
 =?UTF-8?q?=E7=BD=AE=E5=A4=B4=E6=96=87=E4=BB=B6?=
MIME-Version: 1.0
Content-Type: text/plain; charset=UTF-8
Content-Transfer-Encoding: 8bit

---
 CMakeLists.txt | 1 +
 1 file changed, 1 insertion(+)

diff --git a/CMakeLists.txt b/CMakeLists.txt
index 0a5d14fb..459f5eec 100644
--- a/CMakeLists.txt
+++ b/CMakeLists.txt
@@ -31,6 +31,7 @@ target_compile_options(${COMPONENT_NAME} PRIVATE -fno-common)
 target_compile_options(${COMPONENT_NAME} PRIVATE -fno-builtin)
 target_compile_options(${COMPONENT_NAME} PRIVATE -nostdinc)
 target_compile_options(${COMPONENT_NAME} PRIVATE -Werror)
+target_compile_options(${COMPONENT_NAME} PRIVATE -include ${AUTOCONF_PATH})
 
 foreach(inc_path ${INC_PATHS})
     target_include_directories(${COMPONENT_NAME} PRIVATE ${inc_path})
-- 
2.34.1


From 62ecffc3b9e6c8d6bc5dd51f8bfe677e96d18118 Mon Sep 17 00:00:00 2001
From: ninghaoxin <>
Date: Tue, 4 Mar 2025 20:40:36 +0000
Subject: [PATCH 12/16] =?UTF-8?q?=E4=BF=AE=E5=A4=8D=E4=BA=8B=E4=BB=B6?=
 =?UTF-8?q?=E8=AE=A1=E6=95=B0=E8=BF=87=E5=B0=8F=E5=AF=BC=E8=87=B4=E7=9A=84?=
 =?UTF-8?q?=E9=97=AE=E9=A2=98?=
MIME-Version: 1.0
Content-Type: text/plain; charset=UTF-8
Content-Transfer-Encoding: 8bit

---
 CMakeLists.txt         | 4 +---
 src/include/lwip/api.h | 6 +++---
 2 files changed, 4 insertions(+), 6 deletions(-)

diff --git a/CMakeLists.txt b/CMakeLists.txt
index 459f5eec..d9b632fd 100644
--- a/CMakeLists.txt
+++ b/CMakeLists.txt
@@ -10,13 +10,11 @@ project(${COMPONENT_NAME} C)
 add_library(${COMPONENT_NAME} STATIC)
 
 if(CONFIG_ARCH STREQUAL "aarch64")
-target_compile_definitions(${COMPONENT_NAME} PRIVATE -D__arm__)
-target_compile_definitions(${COMPONENT_NAME} PRIVATE -D__AARCH64__)
+target_compile_definitions(${COMPONENT_NAME} PRIVATE -D__aarch64__)
 endif(CONFIG_ARCH STREQUAL "aarch64")
 
 if(CONFIG_ARCH STREQUAL "arm")
 target_compile_definitions(${COMPONENT_NAME} PRIVATE -D__arm__)
-target_compile_definitions(${COMPONENT_NAME} PRIVATE -D__AARCH32__)
 target_compile_options(${COMPONENT_NAME} PRIVATE -mno-unaligned-access)
 endif(CONFIG_ARCH STREQUAL "arm")
 
diff --git a/src/include/lwip/api.h b/src/include/lwip/api.h
index 69bda145..087577ce 100644
--- a/src/include/lwip/api.h
+++ b/src/include/lwip/api.h
@@ -228,12 +228,12 @@ struct netconn {
     struct raw_pcb *raw;
   } pcb;
   struct {
-    s16_t rcvevent;
+    uint32_t rcvevent;
     /** number of times data was ACKed (free send buffer), set by event_callback(),
         tested by select */
-    u16_t sendevent;
+    uint32_t sendevent;
     /** error happened for this socket, set by event_callback(), tested by select */
-    u16_t errevent;
+    uint32_t errevent;
   } events;
   /** the last asynchronous unreported error this netconn had */
   err_t pending_err;
-- 
2.34.1


From 319285002c9b52564dfb15289a2a27b0f0388510 Mon Sep 17 00:00:00 2001
From: sunxiao <<EMAIL>>
Date: Tue, 22 Apr 2025 11:57:45 +0000
Subject: [PATCH 13/16] =?UTF-8?q?=E8=A7=A3=E9=99=A4=E5=87=BD=E6=95=B0netif?=
 =?UTF-8?q?=5Fget=5Floopif()=E7=9A=84=E5=AE=8F=E6=8E=A7=E5=88=B6,=E4=BB=A5?=
 =?UTF-8?q?=E4=BE=9B=E7=B3=BB=E7=BB=9F=E8=BF=9B=E8=A1=8C=E8=B0=83=E7=94=A8?=
MIME-Version: 1.0
Content-Type: text/plain; charset=UTF-8
Content-Transfer-Encoding: 8bit

---
 src/core/netif.c | 4 ++--
 1 file changed, 2 insertions(+), 2 deletions(-)

diff --git a/src/core/netif.c b/src/core/netif.c
index d3a06774..7c1d1f52 100644
--- a/src/core/netif.c
+++ b/src/core/netif.c
@@ -143,12 +143,12 @@ static err_t netif_loop_output_ipv6(struct netif *netif, struct pbuf *p, const i
 
 static struct netif loop_netif;
 
-#if LWIP_TESTMODE
+//#if LWIP_TESTMODE
 struct netif* netif_get_loopif(void)
 {
   return &loop_netif;
 }
-#endif
+//#endif
 
 
 /**
-- 
2.34.1


From 9f1abbfa1b7f704716c7d8bacf287f43faeeb4c4 Mon Sep 17 00:00:00 2001
From: sunxiao <<EMAIL>>
Date: Wed, 23 Apr 2025 07:57:35 +0000
Subject: [PATCH 14/16] =?UTF-8?q?=E5=9C=A8netif=5Floop=5Foutput()=E4=B8=AD?=
 =?UTF-8?q?=E8=B0=83=E7=94=A8eth=5Flwip=5Floopback=5Foutput=5Fhook()?=
 =?UTF-8?q?=E5=87=BD=E6=95=B0=EF=BC=8C=E7=94=A8=E4=BA=8E=E7=BB=9F=E8=AE=A1?=
 =?UTF-8?q?=E7=8E=AF=E5=9B=9E=E7=BD=91=E5=8D=A1=E5=8F=91=E9=80=81=E6=95=B0?=
 =?UTF-8?q?=E6=8D=AE=E4=BF=A1=E6=81=AF?=
MIME-Version: 1.0
Content-Type: text/plain; charset=UTF-8
Content-Transfer-Encoding: 8bit

---
 src/core/netif.c | 5 +++++
 1 file changed, 5 insertions(+)

diff --git a/src/core/netif.c b/src/core/netif.c
index 7c1d1f52..15a0f85a 100644
--- a/src/core/netif.c
+++ b/src/core/netif.c
@@ -1102,6 +1102,7 @@ netif_set_link_callback(struct netif *netif, netif_status_callback_fn link_callb
 #endif /* LWIP_NETIF_LINK_CALLBACK */
 
 #if ENABLE_LOOPBACK
+extern void eth_lwip_loopback_output_hook();
 /**
  * @ingroup netif
  * Send an IP packet to be received on the same netif (loopif-like).
@@ -1146,6 +1147,7 @@ netif_loop_output(struct netif *netif, struct pbuf *p)
   /* Allocate a new pbuf */
   r = pbuf_alloc(PBUF_LINK, p->tot_len, PBUF_RAM);
   if (r == NULL) {
+    eth_lwip_loopback_output_hook(netif, 1, p->tot_len);
     LINK_STATS_INC(link.memerr);
     LINK_STATS_INC(link.drop);
     MIB2_STATS_NETIF_INC(stats_if, ifoutdiscards);
@@ -1157,6 +1159,7 @@ netif_loop_output(struct netif *netif, struct pbuf *p)
   if (((netif->loop_cnt_current + clen) < netif->loop_cnt_current) ||
       ((netif->loop_cnt_current + clen) > LWIP_MIN(LWIP_LOOPBACK_MAX_PBUFS, 0xFFFF))) {
     pbuf_free(r);
+    eth_lwip_loopback_output_hook(netif, 1, p->tot_len);
     LINK_STATS_INC(link.memerr);
     LINK_STATS_INC(link.drop);
     MIB2_STATS_NETIF_INC(stats_if, ifoutdiscards);
@@ -1168,6 +1171,7 @@ netif_loop_output(struct netif *netif, struct pbuf *p)
   /* Copy the whole pbuf queue p into the single pbuf r */
   if ((err = pbuf_copy(r, p)) != ERR_OK) {
     pbuf_free(r);
+    eth_lwip_loopback_output_hook(netif, 1, p->tot_len);
     LINK_STATS_INC(link.memerr);
     LINK_STATS_INC(link.drop);
     MIB2_STATS_NETIF_INC(stats_if, ifoutdiscards);
@@ -1203,6 +1207,7 @@ netif_loop_output(struct netif *netif, struct pbuf *p)
   }
   SYS_ARCH_UNPROTECT(lev);
 
+  eth_lwip_loopback_output_hook(netif, 0, p->tot_len);
   LINK_STATS_INC(link.xmit);
   MIB2_STATS_NETIF_ADD(stats_if, ifoutoctets, p->tot_len);
   MIB2_STATS_NETIF_INC(stats_if, ifoutucastpkts);
-- 
2.34.1


From 77361bae66e1c2d4ac3176e584af9698fbcff9b9 Mon Sep 17 00:00:00 2001
From: sunxiao <<EMAIL>>
Date: Tue, 17 Jun 2025 11:24:04 +0800
Subject: [PATCH 15/16] =?UTF-8?q?=E6=8E=A7=E5=88=B6LwIP=E5=9C=A8=E6=8E=A5?=
 =?UTF-8?q?=E6=94=B6=E5=88=B0=E7=9A=84UDP=E5=8C=85=E4=BD=BF=E7=94=A8?=
 =?UTF-8?q?=E7=9A=84=E6=98=AF=E6=9C=AA=E8=AE=B0=E5=BD=95=E7=9A=84=E6=9C=AC?=
 =?UTF-8?q?=E5=9C=B0=E7=AB=AF=E5=8F=A3=E6=97=B6=EF=BC=8C=E6=98=AF=E5=90=A6?=
 =?UTF-8?q?=E8=87=AA=E5=8A=A8=E5=9B=9E=E5=A4=8D=E7=AB=AF=E5=8F=A3=E4=B8=8D?=
 =?UTF-8?q?=E5=8F=AF=E8=BE=BEICMP=E5=8C=85=20=E5=88=9B=E5=BB=BASOCK=5FRAW?=
 =?UTF-8?q?=E7=B1=BB=E5=9E=8B=E5=A5=97=E6=8E=A5=E5=AD=97=E5=B9=B6=E8=87=AA?=
 =?UTF-8?q?=E8=A1=8C=E5=B0=81=E8=A3=85UDP=E5=8C=85=E4=B8=8E=E5=A4=96?=
 =?UTF-8?q?=E9=83=A8=E8=BF=9B=E8=A1=8C=E9=80=9A=E4=BF=A1=E6=97=B6=E5=B0=B1?=
 =?UTF-8?q?=E4=BC=9A=E5=87=BA=E7=8E=B0=E4=B8=8A=E8=BF=B0=E6=83=85=E5=86=B5?=
MIME-Version: 1.0
Content-Type: text/plain; charset=UTF-8
Content-Transfer-Encoding: 8bit

---
 src/core/udp.c | 2 ++
 1 file changed, 2 insertions(+)

diff --git a/src/core/udp.c b/src/core/udp.c
index 23c2be27..f08aec2f 100644
--- a/src/core/udp.c
+++ b/src/core/udp.c
@@ -416,7 +416,9 @@ udp_input(struct pbuf *p, struct netif *inp)
       if (!broadcast && !ip_addr_ismulticast(ip_current_dest_addr())) {
         /* move payload pointer back to ip header */
         pbuf_header_force(p, (s16_t)(ip_current_header_tot_len() + UDP_HLEN));
+#ifdef CONFIG_LWIP_ENABLE_ICMP_PORT_UNREACH
         icmp_port_unreach(ip_current_is_v6(), p);
+#endif
       }
 #endif /* LWIP_ICMP || LWIP_ICMP6 */
       UDP_STATS_INC(udp.proterr);
-- 
2.34.1


From 8567835297b66ee9f9cbb9f06eb10d086b89641a Mon Sep 17 00:00:00 2001
From: sunxiao <<EMAIL>>
Date: Tue, 8 Jul 2025 14:22:43 +0800
Subject: [PATCH 16/16] =?UTF-8?q?lwip=5Fsock=E7=BB=93=E6=9E=84=E4=BD=93?=
 =?UTF-8?q?=E4=B8=AD=E5=A2=9E=E5=8A=A0=E8=87=AA=E6=97=8B=E9=94=81=E6=88=90?=
 =?UTF-8?q?=E5=91=98=E5=8F=98=E9=87=8Fpoll=5Flock?=
MIME-Version: 1.0
Content-Type: text/plain; charset=UTF-8
Content-Transfer-Encoding: 8bit

---
 src/api/sockets.c                    | 2 +-
 src/include/lwip/priv/sockets_priv.h | 1 +
 2 files changed, 2 insertions(+), 1 deletion(-)

diff --git a/src/api/sockets.c b/src/api/sockets.c
index fc2d5948..749fed50 100644
--- a/src/api/sockets.c
+++ b/src/api/sockets.c
@@ -2624,7 +2624,7 @@ event_callback(struct netconn *conn, enum netconn_evt evt, u16_t len)
   }
   SYS_ARCH_UNPROTECT(lev);
 
-  kpoll_notify(&sock->kfd, 1, eventset, &sock->aio);
+  kpoll_notify(&sock->kfd, 1, eventset, &sock->aio, &sock->poll_lock);
 
   done_socket(sock);
 }
diff --git a/src/include/lwip/priv/sockets_priv.h b/src/include/lwip/priv/sockets_priv.h
index e9e48d13..e7b04d06 100644
--- a/src/include/lwip/priv/sockets_priv.h
+++ b/src/include/lwip/priv/sockets_priv.h
@@ -90,6 +90,7 @@ struct lwip_sock {
 #define LWIP_SOCK_FD_FREE_FREE 2
 #endif
 
+  ttos_spinlock_t poll_lock;
   struct aio_info aio;
   struct kpollfd *kfd;
 };
-- 
2.34.1

