/****************************************************************************
 * fs/vfs/fs_poll.c
 *
 * Licensed to the Apache Software Foundation (ASF) under one or more
 * contributor license agreements.  See the NOTICE file distributed with
 * this work for additional information regarding copyright ownership.  The
 * ASF licenses this file to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance with the
 * License.  You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.  See the
 * License for the specific language governing permissions and limitations
 * under the License.
 *
 ****************************************************************************/

/****************************************************************************
 * Included Files
 ****************************************************************************/

#include <assert.h>
#include <errno.h>
#include <stdbool.h>
#include <time.h>

#include <fs/fs.h>
#include <ttos.h>
#include <ttosProcess.h>
#include <inttypes.h>
#include <time/ktime.h>

#define KLOG_TAG "poll"
#include <klog.h>

/****************************************************************************
 * Private Functions
 ****************************************************************************/

static void poll_default_cb (struct kpollfd *fds);
static int  poll_fdsetup (int fd, struct kpollfd *fds, bool setup);

/****************************************************************************
 * Name: poll_setup
 *
 * Description:
 *   Setup the poll operation for each descriptor in the list.
 *
 ****************************************************************************/

static inline int poll_setup (struct kpollfd *fds, nfds_t nfds, SEMA_ID sem)
{
    unsigned int i;
    unsigned int j;
    int          ret = 0;

    /* Process each descriptor in the list */

    for (i = 0; i < nfds; i++)
    {
        /* Setup the poll descriptor
         *
         * REVISIT: In a multi-threaded environment, one use case might be to
         * share a single, array of struct kpollfd in poll() calls on different
         * threads.  That use case is not supportable here because the non-
         * standard internal fields get reset here on each call to poll()
         * on each thread.
         */

        fds[i].arg            = sem;
        fds[i].cb             = poll_default_cb;
        fds[i].pollfd.revents = 0;
        fds[i].priv           = NULL;

        /* Check for invalid descriptors. "If the value of fd is less than 0,
         * events shall be ignored, and revents shall be set to 0 in that entry
         * on return from poll()."
         *
         * NOTE:  There is a potential problem here.  If there is only one fd
         * and if it is negative, then poll will hang.  From my reading of the
         * spec, that appears to be the correct behavior.
         */

        if (fds[i].pollfd.fd >= 0)
        {
            ret = poll_fdsetup (fds[i].pollfd.fd, &fds[i], true);
        }

        if (ret < 0)
        {
            /* Setup failed for fds[i]. We now need to teardown previously
             * setup fds[0 .. (i - 1)] to release allocated resources and
             * to prevent memory corruption by access to freed/released 'fds'
             * and 'sem'.
             */

            for (j = 0; j < i; j++)
            {
                poll_fdsetup (fds[j].pollfd.fd, &fds[j], false);
            }

            /* Indicate an error on the file descriptor */

            fds[i].pollfd.revents |= POLLERR;
            return ret;
        }
    }

    return 0;
}

/****************************************************************************
 * Name: poll_teardown
 *
 * Description:
 *   Teardown the poll operation for each descriptor in the list and return
 *   the count of non-zero poll events.
 *
 ****************************************************************************/

static inline int poll_teardown (struct kpollfd *fds, nfds_t nfds, int *count,
                                 int ret)
{
    unsigned int i;
    int          status = 0;

    /* Process each descriptor in the list */

    *count = 0;
    for (i = 0; i < nfds; i++)
    {
        if (fds[i].pollfd.fd >= 0)
        {
            status = poll_fdsetup (fds[i].pollfd.fd, &fds[i], false);
        }

        if (status < 0)
        {
            ret = status;
        }

        /* Check if any events were posted */

        if (fds[i].pollfd.revents != 0)
        {
            (*count)++;
        }

        /* Un-initialize the poll structure */

        fds[i].arg = NULL;
        fds[i].cb  = NULL;
    }

    return ret;
}

/****************************************************************************
 * Public Functions
 ****************************************************************************/

/****************************************************************************
 * Name: poll_fdsetup
 *
 * Description:
 *   Configure (or unconfigure) one file/socket descriptor for the poll
 *   operation.  If fds and sem are non-null, then the poll is being setup.
 *   if fds and sem are NULL, then the poll is being torn down.
 *
 ****************************************************************************/

static int poll_fdsetup (int fd, struct kpollfd *fds, bool setup)
{
    struct file *filep;
    int          ret;

    /* Get the file pointer corresponding to this file descriptor */

    ret = fs_getfilep (fd, &filep);
    if (ret < 0)
    {
        return ret;
    }

    assert (filep != NULL);

    /* Let file_poll() do the rest */

    return file_poll (filep, fds, setup);
}

/****************************************************************************
 * Name: poll_default_cb
 *
 * Description:
 *   The default poll callback function, this function do the final step of
 *   poll notification.
 *
 * Input Parameters:
 *   fds - The fds
 *
 * Returned Value:
 *   None
 *
 ****************************************************************************/

static void poll_default_cb (struct kpollfd *fds)
{
    SEMA_ID pollsem;

    if (fds->arg != NULL)
    {
        pollsem = (SEMA_ID)fds->arg;
        if (pollsem->semaControlValue < 1)
        {
            TTOS_ReleaseSema (pollsem);
        }
    }
}

/****************************************************************************
 * Name: kpoll_notify
 *
 * Description:
 *   Notify the poll, this function should be called by drivers to notify
 *   the caller the poll is ready.
 *
 * Input Parameters:
 *   afds     - The fds array
 *   nfds     - Number of fds array
 *   eventset - List of events to check for activity
 *
 * Returned Value:
 *   None
 *
 ****************************************************************************/

void kpoll_notify (struct kpollfd **afds, int nfds, short eventset, struct aio_info* aio, ttos_spinlock_t * lock)
{
    int             i;
    struct kpollfd *fds;
    bool polled = false;

    irq_flags_t flags;

    assert (afds != NULL && nfds >= 1);

    for (i = 0; i < nfds && eventset; i++)
    {
        if (lock)
        {
            spin_lock_irqsave(lock, flags);
        }
        fds = afds[i];
        if (fds != NULL)
        {
            /* The error event must be set in fds->revents */

            fds->pollfd.revents
                |= eventset & (fds->pollfd.events | POLLERR | POLLHUP);
            if ((fds->pollfd.revents & (POLLERR | POLLHUP)) != 0)
            {
                /* Error or Hung up, clear POLLOUT event */

                fds->pollfd.revents &= ~POLLOUT;
            }

            if (fds->pollfd.revents != 0 && fds->cb != NULL)
            {
                polled = true;
                fds->cb (fds);
            }
        }
        if (lock)
        {
            spin_unlock_irqrestore(lock, flags);
        }
    }

    if(!polled && (eventset & POLLIN || eventset & POLLERR) && aio && aio->aio_enable)
    {
        kernel_signal_kill(aio->pid, SIGIO, SI_ASYNCIO, 0);
    }
}

/****************************************************************************
 * Name: file_poll
 *
 * Description:
 *   Low-level poll operation based on struct file.  This is used both to (1)
 *   support detached file, and also (2) by poll_fdsetup() to perform all
 *   normal operations on file descriptors.
 *
 * Input Parameters:
 *   file     File structure instance
 *   fds   - The structure describing the events to be monitored, OR NULL if
 *           this is a request to stop monitoring events.
 *   setup - true: Setup up the poll; false: Teardown the poll
 *
 * Returned Value:
 *  0: Success; Negated errno on failure
 *
 ****************************************************************************/

int file_poll (struct file *filep, struct kpollfd *fds, bool setup)
{
    struct inode *inode;
    int           ret = -ENOSYS;

    assert (filep != NULL);
    inode = filep->f_inode;

    if (inode != NULL)
    {
        /* Is a driver registered? Does it support the poll method?
         * If not, return -ENOSYS
         */

        if ((INODE_IS_DRIVER (inode) || INODE_IS_MQUEUE (inode)
             || INODE_IS_SOCKET (inode) || INODE_IS_PIPE (inode))
            && inode->u.i_ops != NULL && inode->u.i_ops->poll != NULL)
        {
            /* Yes, it does... Setup the poll */

            ret = (int)inode->u.i_ops->poll (filep, fds, setup);
        }

        /* Regular files (and block devices) are always readable and
         * writable. Open Group: "Regular files shall always poll TRUE for
         * reading and writing."
         */

        if (INODE_IS_MOUNTPT (inode) || INODE_IS_BLOCK (inode)
            || INODE_IS_MTD (inode))
        {
            if (setup)
            {
                kpoll_notify (&fds, 1, POLLIN | POLLOUT, NULL, NULL);
            }

            ret = 0;
        }
    }
    else
    {
        kpoll_notify (&fds, 1, POLLERR | POLLHUP, NULL, NULL);
        ret = 0;
    }

    return ret;
}

/****************************************************************************
 * Name: poll
 *
 * Description:
 *   poll() waits for one of a set of file descriptors to become ready to
 *   perform I/O.  If none of the events requested (and no error) has
 *   occurred for any of  the  file  descriptors,  then  poll() blocks until
 *   one of the events occurs.
 *
 * Input Parameters:
 *   fds  - List of structures describing file descriptors to be monitored
 *   nfds - The number of entries in the list
 *   timeout - Specifies an upper limit on the time for which poll() will
 *     block in milliseconds.  A negative value of timeout means an infinite
 *     timeout.
 *
 * Returned Value:
 *   On success, the number of structures that have non-zero revents fields.
 *   A value of 0 indicates that the call timed out and no file descriptors
 *   were ready.  On error, -1 is returned, and errno is set appropriately:
 *
 *   EBADF  - An invalid file descriptor was given in one of the sets.
 *   EFAULT - The fds address is invalid
 *   EINTR  - A signal occurred before any requested event.
 *   EINVAL - The nfds value exceeds a system limit.
 *   ENOMEM - There was no space to allocate internal data structures.
 *   ENOSYS - One or more of the drivers supporting the file descriptor
 *     does not support the poll method.
 *
 ****************************************************************************/

int kpoll (struct kpollfd *kfds, nfds_t nfds, int timeout)
{
    SEMA_ID sem;
    int     count = 0;
    int     ret2;
    int     ret;

    if (nfds == 0 || kfds == NULL)
    {
        errno = EINVAL;
        return -EINVAL;
    }

    /* poll() is a cancellation point */
    TTOS_CreateSemaEx (0, &sem);
    ret = poll_setup (kfds, nfds, sem);
    if (ret >= 0)
    {
        if (timeout == 0)
        {
            /* Poll returns immediately whether we have a poll event or not. */

            ret = 0;
        }
        else
        {
            /* Either wait for either a poll event(s), for a signal to occur,
             * or for the specified timeout to elapse with no event.
             *
             * NOTE: If a poll event is pending (i.e., the semaphore has
             * already been incremented), nxsem_tickwait() will not wait, but
             * will return immediately.
             */
            ret = TTOS_ObtainSema (sem, clock_ms_to_tick(timeout));
            if (ret != TTOS_OK)
            {
                if (ret == TTOS_TIMEOUT)
                {
                    /* Return zero (0) in the event of a timeout */

                    ret = 0;
                }
                else
                {
                    /* Otherwise, return the negated errno value */

                    ret = -ttos_ret_to_errno(ret);
                }
                /* EINTR is the only other error expected in normal operation */
            }
        }

        /* Teardown the poll operation and get the count of events.  Zero will
         * be returned in the case of a timeout.
         *
         * Preserve ret, if negative, since it holds the result of the wait.
         */

        ret2 = poll_teardown (kfds, nfds, &count, ret);
        if (ret != -EINTR && ret2 < 0 && ret >= 0)
        {
            ret = ret2;
        }
    }

    TTOS_DeleteSema (sem);

    if (ret > 0)
    {
        return -ret;
    }
    else if (ret < 0)
    {
        return ret;
    }
    else
    {
        return count;
    }
}
