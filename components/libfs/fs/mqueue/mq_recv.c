#include <assert.h>
#include <errno.h>
#include <fcntl.h>
#include <mqueue.h>
#include <stdlib.h>
#include <ttos.h>
#include <ttosInterHal.h>

#include "../inode/inode.h"
#include "mqueue.h"

// todo fix msglen
ssize_t file_mq_receive (struct file *mq, char *msg, size_t msglen,
                         unsigned int *prio, T_UWORD wait_ticks)
{
    struct mqueue_inode_s *msgq;
    ssize_t                ret = 0;
    T_TTOS_ReturnCode      ttos_ret;
    T_UWORD                my_count;
    short                  eventset = 0;
    T_UWORD                kmsglen  = msglen;

    msgq = mq->f_inode->i_private;

    if(!INODE_IS_MQUEUE(mq->f_inode))
    {
        return -EBADFD;
    }

    if (msglen < msgq->ttos_msgq->maxMsgSize)
    {
        return -EMSGSIZE;
    }

    if ((msgq->ttos_msgq->attr.mq_flags & O_NONBLOCK)
        && (msgq->ttos_msgq->attr.mq_curmsgs == 0))
    {
        return -EAGAIN;
    }

    if (((mq->f_oflags & O_ACCMODE) != O_RDWR)
        && ((mq->f_oflags & O_ACCMODE) != O_RDONLY))
    {
        return -EBADF;
    }

    ttos_ret
        = TTOS_ReceiveMsgq (msgq->ttos_msgq, msg, &kmsglen,
                            wait_ticks ? TTOS_WAIT : TTOS_NO_WAIT, wait_ticks);
    if (ttos_ret != TTOS_OK)
    {
		ret = -ttos_ret_to_errno(ttos_ret);
    }
    else
    {
        TBSP_MSR_TYPE msr = 0;
        if (prio != NULL)
        {
            TBSP_GLOBALINT_DISABLE_WITH_LOCK (msr);
            *prio = TTOS_MQ_PRIO_CONVERT (
                ttosGetCurrentCpuRunningTask ()->wait.count);
            TBSP_GLOBALINT_ENABLE_WITH_LOCK (msr);
        }

        TTOS_GetMsgqPendingCount (msgq->ttos_msgq, &my_count);
        if (my_count < msgq->ttos_msgq->maxPendingMsg)
        {
            msgq->ttos_msgq->attr.mq_curmsgs = my_count;
            eventset |= POLLOUT;
        }

        kpoll_notify (msgq->fds, CONFIG_FS_MQUEUE_NPOLLWAITERS, eventset, NULL, &msgq->polllock);
        ret = kmsglen;
    }

    return ret;
}

ssize_t vfs_mq_receive (mqd_t mqdes, char *msg, size_t msglen,
                        unsigned int *prio, T_UWORD wait_ticks)
{
    struct file *filep;
    int          ret;

    ret = fs_getfilep (mqdes, &filep);
    if (ret < 0)
    {
        return ret;
    }

    return file_mq_receive (filep, msg, msglen, prio, wait_ticks);
}
