#include <assert.h>
#include <errno.h>
#include <fcntl.h>
#include <limits.h>
#include <mqueue.h>
#include <stdlib.h>
#include <ttos.h>

#include "../inode/inode.h"
#include "mqueue.h"

int file_mq_send (struct file *mq, const char *msg, size_t msglen,
                  unsigned int prio, T_UWORD wait_ticks)
{
    struct mqueue_inode_s *msgq;
    ssize_t                ret = 0;
    T_TTOS_ReturnCode      ttos_ret;
    T_UWORD                my_count;
    short                  eventset = 0;

    msgq = mq->f_inode->i_private;

    if(!INODE_IS_MQUEUE(mq->f_inode))
    {
        return -EBADFD;
    }

    if (msglen > msgq->ttos_msgq->attr.mq_msgsize)
    {
        return -EMSGSIZE;
    }

    if ((msgq->ttos_msgq->attr.mq_curmsgs >= msgq->ttos_msgq->attr.mq_maxmsg)
        && (msgq->ttos_msgq->attr.mq_flags & O_NONBLOCK))
    {
        return -EAGAIN;
    }

    if (((mq->f_oflags & O_WRONLY) == 0) && ((mq->f_oflags & O_RDWR) == 0))
    {
        return -EBADF;
    }

    if (prio >= MQ_PRIO_MAX || prio < 0)
    {
        return -EINVAL;
    }

    /* ttos msg queue只支持32级优先级 */
    if (prio > TTOS_MQ_PRIO_MAX)
    {
        prio = TTOS_MQ_PRIO_MAX;
    }

    ttos_ret = TTOS_SendMsgq (
        msgq->ttos_msgq, (void *)msg, msglen,
        (wait_ticks ? TTOS_WAIT : TTOS_NO_WAIT)
            | TTOS_MSGQ_SET_PRIORITY (TTOS_MQ_PRIO_CONVERT (prio)),
        wait_ticks);
    if (ttos_ret != TTOS_OK)
    {
        ret = -ETIMEDOUT;
    }
    else
    {
        TTOS_GetMsgqPendingCount (msgq->ttos_msgq, &my_count);

        if (my_count)
        {
            msgq->ttos_msgq->attr.mq_curmsgs = my_count;
            eventset |= POLLIN;
        }
        kpoll_notify (msgq->fds, CONFIG_FS_MQUEUE_NPOLLWAITERS, eventset, NULL, &msgq->polllock);
        ret = msglen;
    }

    return ret;
}

/****************************************************************************
 * Name: vfs_mq_send
 *
 * Description:
 *   This function adds the specified message (msg) to the message queue
 *   (mqdes).  This is an internal OS interface.  It is functionally
 *   equivalent to mq_send() except that:
 *
 *   - It is not a cancellation point, and
 *   - It does not modify the errno value.
 *
 *  See comments with mq_send() for a more complete description of the
 *  behavior of this function
 *
 * Input Parameters:
 *   mqdes  - Message queue descriptor
 *   msg    - Message to send
 *   msglen - The length of the message in bytes
 *   prio   - The priority of the message
 *
 * Returned Value:
 *   This is an internal OS interface and should not be used by applications.
 *   It follows the NuttX internal error return policy:  Zero (OK) is
 *   returned on success.  A negated errno value is returned on failure.
 *   (see mq_send() for the list list valid return values).
 *
 ****************************************************************************/

int vfs_mq_send (mqd_t mqdes, const char *msg, size_t msglen, unsigned int prio,
                 T_UWORD wait_ticks)
{
    struct file *filep;
    int          ret;

    ret = fs_getfilep (mqdes, &filep);
    if (ret < 0)
    {
        return ret;
    }

    return file_mq_send (filep, msg, msglen, prio, wait_ticks);
}